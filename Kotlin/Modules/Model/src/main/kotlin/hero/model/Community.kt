package hero.model

import java.time.Instant
import java.util.UUID

data class Community(
    val id: UUID,
    val name: String,
    val description: String,
    val slug: String,
    val ownerId: String,
    val membersCount: Long,
    val image: ImageAsset?,
    val createdAt: Instant,
    val updatedAt: Instant,
    val deletedAt: Instant?,
)

enum class CommunityMemberStatus {
    ACTIVE,
    LEFT,
    KICKED,
    SUBSCRIPTION_EXPIRED,
}
