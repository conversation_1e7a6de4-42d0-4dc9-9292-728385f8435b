package hero.model

import com.fasterxml.jackson.annotation.JsonAlias
import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonProperty
import hero.baseutils.SystemEnv
import hero.core.annotation.NoArg
import hero.model.GjirafaStatus.NOT_APPLICABLE
import java.time.Instant

data class GjirafaResponse<T>(
    val success: Boolean,
    val statusCode: Int,
    val result: T,
    val errors: List<Any>,
    val messages: List<String>,
    val resultInfo: Any?,
)

data class GjirafaResponsePage<T>(
    val items: List<T>,
    val pageSize: Int,
    val currentPage: Int,
    val totalPages: Int,
    val totalCount: Int,
    val beforeCursor: String?,
)

data class GjirafaUploadResponseResult(
    val url: String,
    val requestKey: String,
    val uploadFileDetailsId: Long?,
)

data class GjirafaEncodeResponseResult(
    // note that video encode results in `name`, audio results in `publicId`
    val name: String?,
    val publicId: String?,
    val originalFile: String?,
) {
    val assetId: String
        get() = name ?: publicId ?: error("Cannot get assetId as both assetId and name were missing.")
}

@NoArg
data class GjirafaQualityType(
    val videoId: String?,
    val audioId: String?,
    val qualityType: String?,
    val streamUrl: String?,
    val status: GjirafaStatus,
    val duration: Double,
    val size: Long?,
    val width: Int,
    val height: Int,
    val bitrate: Int,
    val audioCodec: String?,
    val audioBitrate: Int,
    val audioSampleRate: Int,
    val progress: Int?,
    // start and end of encoding process
    val startDateTime: Instant?,
    val endDateTime: Instant?,
) {
    val assetId: String? = videoId ?: audioId
}

enum class GjirafaStatus {
    @JsonAlias("AnyStatus", "ANY_STATUS", "Any", "Submitted", "Pending")
    PROCESSING,
    /** empty output because of the encoding template */
    @JsonAlias(value = ["NotApplicable"])
    NOT_APPLICABLE,
    /** when assets are not completely encoded, but already playable */
    @JsonAlias(value = ["PartiallyCompleted", "PartialyCompleted", "Generated"])
    PARTIALLY_COMPLETED,
    COMPLETE,
    /** after 5 retries file can’t be encoded */
    @JsonAlias(value = ["InvalidRequest", "NotComplete", "Canceled"])
    ERROR,
}

@NoArg
data class GjirafaStream(
    val assetType: GjirafaAssetType,
    val streamType: String,
    val root: String?,
    @JsonProperty("qualityTypes")
    val qualityTypesRaw: List<GjirafaQualityType>,
) {
    // use only internally, avoid passing to FE to prevent "Should never call `set()` on setterless property"
    @get:JsonIgnore
    val qualityTypes: List<GjirafaQualityType>
        get() = qualityTypesRaw.filter { it.status != NOT_APPLICABLE }

    // see: https://linear.app/herohero/issue/HH-1357/replace-root-url-with-otpf-before-video-is-being-processed
    val streamUrl: String?
        get() = qualityTypes.firstOrNull { it.qualityType == "Original" }?.streamUrl ?: root
}

enum class GjirafaAssetType {
    @JsonProperty("Audio")
    AUDIO,
    @JsonProperty("Video")
    VIDEO,
    // requirement for chapters
    @JsonProperty("Others")
    OTHERS,
}

@NoArg
data class GjirafaStreamResponse(
    val contentId: String?,
    val keyId: String?,
    val progress: Int?,
    val progressTillReadiness: Int?,
    val status: GjirafaStatus,
    val insertDate: Instant?,
    val encodingProcessRemainingTime: Double?,
    val encodingProcessFinishTime: Instant?,
    @JsonProperty("streams")
    val streamsRaw: List<GjirafaStream>,
) {
    /** Gjirafa lists all streams even though not available. These must be removed to be handled properly. */
    @get:JsonIgnore
    val streams: List<GjirafaStream>
        get() = streamsRaw.filter { it.qualityTypes.isNotEmpty() }
}

@NoArg
data class GjirafaMediaDetailV2(
    @JsonAlias("mediaId")
    val publicId: String,
    val name: String?,
    val title: String,
    @JsonAlias("videoAuthor")
    val author: String?,
    val publishDate: String?,
    val description: String?,
    val thumbnailUrl: String?,
    val filmStripUrl: String?,
    val filmStripVttUrl: String?,
    val customParameters: List<GjirafaMetadata>?,
)

@NoArg
data class GjirafaMetadata(
    val id: Long,
    val key: String,
    val value: String,
)

@NoArg
data class GjirafaMediaDetail(
    val filmStrip: String?,
    val thumbnail: String?,
    val originalFile: String?,
    val playbackUrl: String?,
    val streamResponse: GjirafaStreamResponse?,
    val thumbnailBlurhash: String?,
    val chaptersVtt: String?,
)

@NoArg
data class GjirafaAsset(
    var progressTillReadiness: Int = 0,
    var progressTillCompleteness: Int = 0,
    var width: Int? = null,
    var height: Int? = null,
    @Deprecated("Migrate to `encodeKey`.")
    var key: String?,
    // eg.:https://cdn.vpplayer.tech/aaaaaaaa/pLZw5imXMLYIFUFQLCeCag==,7991601123/encode/vjsnlpex/hls/master_file.m3u8
    var videoStreamUrl: String? = null,
    // eg.: https://cdn.vpplayer.tech/aaaaaaaa/pLZw5imXMLYIFUFQLCeCag==,7991601123/encode/vjsnlpex/hls/360p/index.m3u8
    var audioStreamUrl: String?,
    // eg.: https://cdn.vpplayer.tech/aaaaaaaa/pLZw5imXMLYIFUFQLCeCag==,7991601123/encode/vjsnlpex/mp3/320kbps.mp3
    var audioStaticUrl: String?,
    var chaptersVttUrl: String?,
    var hasAudio: Boolean,
    var hasVideo: Boolean,
    var id: String,
    // used to quickly hide a problematic assets
    var hidden: Boolean,
    var duration: Double,
    var audioByteSize: Long?,
    var status: GjirafaStatus,
    var debugDetail: GjirafaMediaDetail? = null,
    @Deprecated("Migrate to `drmKey`.")
    var keyId: String? = null,
    val encodingRemainingTime: Double?,
    val encodingFinishTime: Instant?,
    val createdAt: Instant?,
    val projectId: String?,
    val thumbnailBlurhash: String?,
    // TODO do not show these for regular viewers
    val originalFileName: String? = null,
    val originalFileSize: Long? = null,
    // TODO remove default value once all production gjirafa assets are refreshed
    val imageKey: String = "tNTT3UXuKWZTxrJpT36D0w==,4837254998",
) {
    val drmKey: String?
        get() = keyId
    val encodeKey: String?
        get() = key
    private val prefix: String
        get() = "https://cdn.vpplayer.tech/${projectId ?: SystemEnv.gjirafaProject}"
    val previewAnimatedUrl: String
        get() = previewUrl(isAnimated = true)
    val previewStaticUrl: String
        get() = previewUrl(isAnimated = false)
    val previewStripUrl: String
        get() = "$prefix/$encodeKey/encode/$id/thumbnails/filmstrip.vtt"

    private fun previewUrl(isAnimated: Boolean): String =
        "$prefix/$imageKey/images/$id/" +
            (if (drmKey != null) "original-" else "") +
            (if (isAnimated) "animated" else "thumbnail") +
            ".webp"
}

data class GjirafaAssetMinimal(
    val id: String,
    val status: GjirafaStatus,
)

@NoArg
data class GjirafaLiveAsset(
    val id: String,
    val playbackUrl: String,
    val channelPublicId: String,
    val liveStatus: LiveVideoStatus,
)

enum class LiveVideoStatus {
    LIVE,
    OFFLINE,
    PROCESSING,
    INTERRUPTED,
}

@NoArg
data class GjirafaUpload(
    val url: String,
    val requestKey: String,
    val filePath: String,
)

data class GjirafaUploadInfo(
    val uploadId: String,
    val requestKey: String,
    val presignedUrl: List<GjirafaPartUrl>,
    val uploadFileDetailsId: Long?,
)

data class GjirafaPartUrl(
    val partNumber: Int,
    val presignedUrl: String,
)

data class GjirafaEncodeRequest(
    val requestKey: String,
    val parts: List<GjirafaPartToEncode>,
)

data class GjirafaPartToEncode(
    val partNumber: Int,
    // do not remove this annotation as this is required because of Java Beans specification
    @get:JsonProperty("eTag")
    val eTag: String?,
)
