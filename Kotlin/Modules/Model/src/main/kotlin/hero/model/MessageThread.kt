package hero.model

import hero.core.data.EntityCollection
import java.time.Instant
import kotlin.math.absoluteValue

data class MessageThread(
    val userIds: List<String> = listOf(),
    val createdAt: Instant = Instant.now(),
    val lastMessageAt: Instant? = null,
    val lastMessageBy: String? = null,
    val lastMessageId: String? = null,
    val posts: Long = 0L,
    val checks: Map<String, Instant> = mapOf(),
    val seens: Map<String, Instant> = mapOf(),
    val canMessage: Map<String, Boolean> = mapOf(),
    val archivedFor: List<String> = listOf(),
    val deletedFor: List<String> = listOf(),
    val activeFor: List<String> = userIds,
    val deletes: Map<String, Instant> = mapOf(),
    val emailNotified: Boolean = false,
    val id: String = "${System.currentTimeMillis()}-${userIds.hashCode().absoluteValue}",
) {
    companion object : EntityCollection<MessageThread> {
        override val collectionName: String = "message-threads"
    }
}

data class MessageThreadDto(
    val id: String?,
    val attributes: MessageThreadDtoAttributes,
    val relationships: MessageThreadDtoRelationships,
) {
    val type: String = "message-thread"
}

data class MessageThreadDtoAttributes(
    val createdAt: Instant?,
    val canPost: Boolean?,
    val relation: SubscriptionRelationType?,
    val commonCreators: List<String> = emptyList(),
    val seenAt: Instant?,
    val checkedAt: Instant?,
    val lastMessageAt: Instant?,
    val deleted: Boolean = false,
    val archived: Boolean = false,
    val deletedAt: Instant? = null,
)

data class MessageThreadDtoRelationships(
    var users: List<UserDtoRelationship>,
    var lastPost: PostDtoRelationship?,
)

data class MessageThreadDtoV2Relationship(
    val id: String,
) {
    val type: String = "message-thread"
}
