package hero.model

import hero.core.annotation.NoArg
import hero.core.data.EntityCollection
import java.time.Instant

@NoArg
data class AppleCharge(
    val appleTransactionId: String,
    val appleReferenceId: String,
    val createdAt: Instant,
    val userId: String,
    val creatorId: String,
    val tierId: String,
    val targetAccountId: String,
    val stripeTransferId: String?,
    val transferredAt: Instant?,
    val currencyStore: Currency,
    val conversionRateCents: Long?,
    val priceStoreCents: Long,
    val priceFeeHeroheroCents: Long,
) {
    companion object : EntityCollection<AppleCharge> {
        override val collectionName: String = "apple-charges"
    }
}
