package hero.gjirafa

import hero.baseutils.SystemEnv
import hero.exceptions.http.ForbiddenException
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertNotNull
import org.junit.jupiter.api.assertThrows
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class GjirafaUploadsServiceIT {
    private val projectId = SystemEnv.gjirafaProject

    private val service = GjirafaUploadsService(
        projectId = projectId,
        apiKey = SystemEnv.gjirafaApiKey,
        imageKey = SystemEnv.gjirafaImageKey,
    )

    @Test
    fun `generate uploads url`() {
        val uploadResponse = service.uploadsUrl(
            userId = "__testing-user",
            mimeType = "video/mp4",
            fileName = "testing-filename.mp4",
        )
        assertTrue("https://$projectId." in uploadResponse.url)
        assertNotNull(uploadResponse.uploadFileDetailsId)
    }

    @Test
    fun `generate uploads multipart`() {
        val uploadResponse = service.uploadsMultipart(
            userId = "__testing-user",
            partsNumber = 2,
            contentLength = 1024,
            mimeType = "video/mp4",
            fileName = "testing-filename.mp4",
        )
        assertTrue(uploadResponse.uploadId.isNotBlank())
        assertTrue(uploadResponse.requestKey.isNotBlank())
        assertEquals(2, uploadResponse.presignedUrl.size)
        assertNotNull(uploadResponse.uploadFileDetailsId)
    }

    @Test
    fun `check access for video`() {
        val videoId = "vjsnygay"
        val ownerUserId = "bxwcagimmnijj"
        // let's first set different userId to the video
        service.storeMeta(userId = "none", assetId = videoId, postId = null)
        Thread.sleep(200)
        // then check the access is denied
        assertThrows<ForbiddenException> {
            service.assertAuthor("some", videoId)
        }
        service.assertAuthor("none", videoId)
        // the put back the original one
        service.storeMeta(userId = ownerUserId, assetId = videoId, postId = null)
        Thread.sleep(200)
        // and test it is accepted as in the beginning
        service.assertAuthor(ownerUserId, videoId)
    }

    @Test
    fun `check access for audio`() {
        val audioId = "bbgjrcfsk"
        val ownerUserId = "bxwcagimmnijj"
        // let's first set different userId to the video
        service.storeMeta(userId = "none", assetId = audioId, postId = null)
        Thread.sleep(200)
        // then check the access is denied
        assertThrows<ForbiddenException> {
            service.assertAuthor("some", audioId)
        }
        service.assertAuthor("none", audioId)
        // the put back the original one
        service.storeMeta(userId = ownerUserId, assetId = audioId, postId = null)
        Thread.sleep(200)
        // and test it is accepted as in the beginning
        service.assertAuthor(ownerUserId, audioId)
    }
}
