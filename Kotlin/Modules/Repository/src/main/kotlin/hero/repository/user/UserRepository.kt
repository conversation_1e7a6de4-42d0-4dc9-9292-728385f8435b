package hero.repository.user

import hero.exceptions.http.NotFoundException
import hero.jackson.fromJson
import hero.jackson.toJson
import hero.model.ImageAsset
import hero.model.NotificationsEnabled
import hero.model.Role
import hero.model.SupportCounts
import hero.model.User
import hero.model.UserStatus
import hero.sql.jooq.Tables.NOTIFICATION_SETTINGS
import hero.sql.jooq.Tables.USER
import hero.sql.jooq.tables.records.NotificationSettingsRecord
import hero.sql.jooq.tables.records.UserRecord
import org.jooq.DSLContext
import org.jooq.JSONB
import org.jooq.Record
import org.jooq.ResultQuery
import org.jooq.SelectWhereStep
import org.jooq.exception.NoDataFoundException
import java.time.Instant

/**
 * In the future we could consider some CRUD interface such as Spring data has.
 */
class UserRepository(
    lazyContext: Lazy<DSLContext>,
) {
    private val context: DSLContext by lazyContext

    constructor(context: <PERSON><PERSON>ontext) : this(lazy { context })

    fun save(user: User): User {
        val userRecord = UserRecord()
            .apply {
                id = user.id
                name = user.name
                email = user.email
                emailVerified = user.emailVerified

                bio = user.bio
                bioHtml = user.bioHtml
                bioEn = user.bioEn
                bioHtmlEn = user.bioHtmlEn

                path = user.path
                role = user.role.name

                language = user.language
                status = user.status.name

                firebaseId = user.firebaseId
                facebookId = user.facebookId
                googleId = user.googleId
                airtableId = user.airTableId

                isExplicit = user.explicit
                isFeatured = user.featured
                featuredInLanguages = user.featuredBy.toTypedArray()
                hasLivestreams = user.hasLivestreams
                hasSpotifyExport = user.hasSpotifyExport
                hasDrm = user.hasDrm
                hasRssFeed = user.hasRssFeed
                subscribersSingleCoupon = user.subscribersSingleCoupon

                if (user.image != null) {
                    profileImageUrl = user.image?.id
                    profileImageWidth = user.image?.width
                    profileImageHeight = user.image?.height
                }

                subscribersCount = user.counts.supporters.toInt()
                subscribersCountThreshold = user.counts.supportersThreshold?.toInt()
                subscriptionsCount = user.counts.supporting.toInt()
                incomeRaw = user.counts.incomes ?: 0
                incomeClean = user.counts.incomesClean ?: 0
                invoicesCount = user.counts.invoices?.toInt() ?: 0
                postsCount = user.counts.posts?.toInt() ?: 0
                payments = user.counts.payments?.toInt() ?: 0
                pendingRequestsCount = user.counts.pendingRequests?.toInt() ?: 0

                customerIds = JSONB.valueOf(user.customerIds.toJson())

                discord = user.discord?.let { JSONB.valueOf(it.toJson()) }
                spotify = user.spotify?.let { JSONB.valueOf(it.toJson()) }
                spotifyUri = user.spotifyUri
                spotifyFeedReady = user.spotifyFeedReady
                gjirafaLivestream = user.gjirafaLivestream?.let { JSONB.valueOf(it.toJson()) }
                analytics = user.analytics.let { JSONB.valueOf(it.toJson()) }

                creator = user.creator.let { JSONB.valueOf(it.toJson()) }
                company = user.company?.let { JSONB.valueOf(it.toJson()) }

                createdAt = user.created
                updatedAt = user.created
                deletedAt = user.deletedAt
                pathChangedAt = user.pathChanged
                oneStopShopAt = user.oneStopShopAt
                privacyPolicyEffectiveAt = user.privacyPolicyEffectiveAt
                lastPostAt = user.lastPostAt
                lastChargeFailedAt = user.lastChargeFailedAt
                verifiedAt = user.verifiedAt

                isOfAge = user.isOfAge
                moderatorPermissions = user.moderatorPermissions
                canCreateCommunity = user.canCreateCommunity
                this.touched(USER.OWNED_COMMUNITIES_COUNT, false)
                this.touched(USER.JOINED_COMMUNITIES_COUNT, false)
            }

        val updateUserRecord = userRecord.copy().apply {
            updatedAt = Instant.now()
            this.touched(USER.OWNED_COMMUNITIES_COUNT, false)
            this.touched(USER.JOINED_COMMUNITIES_COUNT, false)
        }
        val notificationRecord = NotificationSettingsRecord().apply {
            userId = user.id
            emailNewPost = user.notificationsEnabled.emailNewPost
            emailNewDm = user.notificationsEnabled.emailNewDm
            newsletter = user.notificationsEnabled.newsletter
            termsChanged = user.notificationsEnabled.termsChanged
            pushNewPost = user.notificationsEnabled.pushNewPost
            pushNewComment = user.notificationsEnabled.pushNewComment
        }

        context.insertInto(USER)
            .set(userRecord)
            .onDuplicateKeyUpdate()
            .set(updateUserRecord)
            .execute()

        context.insertInto(NOTIFICATION_SETTINGS)
            .set(notificationRecord)
            .onDuplicateKeyUpdate()
            .set(notificationRecord)
            .execute()

        return user
    }

    fun getById(userId: String): User {
        return selectUserFields(context)
            .from(USER)
            .join(NOTIFICATION_SETTINGS).on(USER.ID.eq(NOTIFICATION_SETTINGS.USER_ID))
            .where(USER.ID.eq(userId))
            .runCatching { JooqUserHelper.mapRecordToEntity(fetchSingle()) }
            .getOrElse {
                when (it) {
                    is NoDataFoundException -> throw NotFoundException()
                    else -> throw it
                }
            }
    }

    fun findById(userId: String): User? {
        return selectUserFields(context)
            .from(USER)
            .join(NOTIFICATION_SETTINGS).on(USER.ID.eq(NOTIFICATION_SETTINGS.USER_ID))
            .where(USER.ID.eq(userId))
            .fetchOne()
            ?.let {
                JooqUserHelper.mapRecordToEntity(it)
            }
    }

    fun find(condition: SelectWhereStep<out Record>.() -> ResultQuery<out Record>): List<User> {
        val fetchResult = selectUserFields(context)
            .from(USER)
            .join(NOTIFICATION_SETTINGS).on(USER.ID.eq(NOTIFICATION_SETTINGS.USER_ID))
            .condition()
            .fetch()

        return fetchResult.map { JooqUserHelper.mapRecordToEntity(it) }
    }

    fun findSingle(condition: SelectWhereStep<out Record>.() -> ResultQuery<out Record>): User? {
        return selectUserFields(context)
            .from(USER)
            .join(NOTIFICATION_SETTINGS).on(USER.ID.eq(NOTIFICATION_SETTINGS.USER_ID))
            .condition()
            .fetchOne()
            ?.let {
                JooqUserHelper.mapRecordToEntity(it)
            }
    }
}

private fun selectUserFields(context: DSLContext) = context.select(JooqUserHelper.userFields)

object JooqUserHelper {
    fun mapRecordToEntity(record: Record): User {
        val profileImage = if (record[USER.PROFILE_IMAGE_URL] != null) {
            ImageAsset(
                id = record[USER.PROFILE_IMAGE_URL],
                width = record[USER.PROFILE_IMAGE_WIDTH],
                height = record[USER.PROFILE_IMAGE_HEIGHT],
            )
        } else {
            null
        }

        val supportCounts = SupportCounts(
            supporters = record[USER.SUBSCRIBERS_COUNT].toLong(),
            supportersThreshold = record[USER.SUBSCRIBERS_COUNT_THRESHOLD]?.toLong(),
            supporting = record[USER.SUBSCRIPTIONS_COUNT].toLong(),
            incomesClean = record[USER.INCOME_CLEAN],
            incomes = record[USER.INCOME_RAW],
            invoices = record[USER.INVOICES_COUNT].toLong(),
            posts = record[USER.POSTS_COUNT].toLong(),
            payments = record[USER.PAYMENTS].toLong(),
            pendingRequests = record[USER.PENDING_REQUESTS_COUNT].toLong(),
        )

        val notificationSettings = NotificationsEnabled(
            emailNewPost = record[NOTIFICATION_SETTINGS.EMAIL_NEW_POST],
            emailNewDm = record[NOTIFICATION_SETTINGS.EMAIL_NEW_DM],
            newsletter = record[NOTIFICATION_SETTINGS.NEWSLETTER],
            termsChanged = record[NOTIFICATION_SETTINGS.TERMS_CHANGED],
            pushNewComment = record[NOTIFICATION_SETTINGS.PUSH_NEW_COMMENT],
            pushNewPost = record[NOTIFICATION_SETTINGS.PUSH_NEW_POST],
        )

        return User(
            id = record[USER.ID],
            name = record[USER.NAME],
            email = record[USER.EMAIL],
            emailVerified = record[USER.EMAIL_VERIFIED] ?: false,
            bio = record[USER.BIO],
            image = profileImage,
            bioHtml = record[USER.BIO_HTML],
            bioEn = record[USER.BIO_EN],
            bioHtmlEn = record[USER.BIO_HTML_EN],
            path = record[USER.PATH],
            role = Role.valueOf(record[USER.ROLE]),
            language = record[USER.LANGUAGE],
            status = UserStatus.valueOf(record[USER.STATUS]),
            firebaseId = record[USER.FIREBASE_ID],
            facebookId = record[USER.FACEBOOK_ID],
            googleId = record[USER.GOOGLE_ID],
            airTableId = record[USER.AIRTABLE_ID],
            explicit = record[USER.IS_EXPLICIT],
            featured = record[USER.IS_FEATURED],
            featuredBy = record[USER.FEATURED_IN_LANGUAGES].toList(),
            hasLivestreams = record[USER.HAS_LIVESTREAMS],
            hasSpotifyExport = record[USER.HAS_SPOTIFY_EXPORT],
            hasDrm = record[USER.HAS_DRM],
            hasRssFeed = record[USER.HAS_RSS_FEED],
            subscribersSingleCoupon = record[USER.SUBSCRIBERS_SINGLE_COUPON],
            customerIds = record[USER.CUSTOMER_IDS].data().fromJson(),
            discord = record[USER.DISCORD]?.data()?.fromJson(),
            spotify = record[USER.SPOTIFY]?.data()?.fromJson(),
            spotifyUri = record[USER.SPOTIFY_URI],
            spotifyFeedReady = record[USER.SPOTIFY_FEED_READY],
            gjirafaLivestream = record[USER.GJIRAFA_LIVESTREAM]?.data()?.fromJson(),
            analytics = record[USER.ANALYTICS].data().fromJson(),
            creator = record[USER.CREATOR].data().fromJson(),
            company = record[USER.COMPANY]?.data()?.fromJson(),
            created = record[USER.CREATED_AT],
            deletedAt = record[USER.DELETED_AT],
            pathChanged = record[USER.PATH_CHANGED_AT],
            oneStopShopAt = record[USER.ONE_STOP_SHOP_AT],
            privacyPolicyEffectiveAt = record[USER.PRIVACY_POLICY_EFFECTIVE_AT],
            lastPostAt = record[USER.LAST_POST_AT],
            lastChargeFailedAt = record[USER.LAST_CHARGE_FAILED_AT],
            verifiedAt = record[USER.VERIFIED_AT],
            isOfAge = record[USER.IS_OF_AGE],
            moderatorPermissions = record[USER.MODERATOR_PERMISSIONS],
            counts = supportCounts,
            notificationsEnabled = notificationSettings,
            canCreateCommunity = record[USER.CAN_CREATE_COMMUNITY],
        )
    }

    val userFields = listOf(
        USER.PROFILE_IMAGE_URL,
        USER.PROFILE_IMAGE_WIDTH,
        USER.PROFILE_IMAGE_HEIGHT,
        USER.PENDING_REQUESTS_COUNT,
        USER.SUBSCRIBERS_COUNT,
        USER.SUBSCRIBERS_COUNT_THRESHOLD,
        USER.SUBSCRIPTIONS_COUNT,
        USER.INCOME_CLEAN,
        USER.INCOME_RAW,
        USER.INVOICES_COUNT,
        USER.POSTS_COUNT,
        USER.PAYMENTS,
        USER.ID,
        USER.NAME,
        USER.EMAIL,
        USER.EMAIL_VERIFIED,
        USER.BIO,
        USER.BIO_HTML,
        USER.BIO_EN,
        USER.BIO_HTML_EN,
        USER.PATH,
        USER.ROLE,
        USER.LANGUAGE,
        USER.STATUS,
        USER.FIREBASE_ID,
        USER.FACEBOOK_ID,
        USER.GOOGLE_ID,
        USER.AIRTABLE_ID,
        USER.IS_EXPLICIT,
        USER.IS_FEATURED,
        USER.FEATURED_IN_LANGUAGES,
        USER.HAS_LIVESTREAMS,
        USER.HAS_SPOTIFY_EXPORT,
        USER.HAS_DRM,
        USER.HAS_RSS_FEED,
        USER.SUBSCRIBERS_SINGLE_COUPON,
        USER.CUSTOMER_IDS,
        USER.DISCORD,
        USER.SPOTIFY,
        USER.SPOTIFY_URI,
        USER.SPOTIFY_FEED_READY,
        USER.GJIRAFA_LIVESTREAM,
        USER.ANALYTICS,
        USER.CREATOR,
        USER.COMPANY,
        USER.CREATED_AT,
        USER.DELETED_AT,
        USER.PATH_CHANGED_AT,
        USER.ONE_STOP_SHOP_AT,
        USER.PRIVACY_POLICY_EFFECTIVE_AT,
        USER.LAST_POST_AT,
        USER.LAST_CHARGE_FAILED_AT,
        USER.VERIFIED_AT,
        USER.IS_OF_AGE,
        USER.MODERATOR_PERMISSIONS,
        USER.CAN_CREATE_COMMUNITY,
        NOTIFICATION_SETTINGS.EMAIL_NEW_POST,
        NOTIFICATION_SETTINGS.EMAIL_NEW_DM,
        NOTIFICATION_SETTINGS.NEWSLETTER,
        NOTIFICATION_SETTINGS.TERMS_CHANGED,
        NOTIFICATION_SETTINGS.PUSH_NEW_POST,
        NOTIFICATION_SETTINGS.PUSH_NEW_COMMENT,
    )
}
