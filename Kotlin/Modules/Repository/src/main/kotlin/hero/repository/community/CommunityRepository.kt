package hero.repository.community

import hero.exceptions.http.NotFoundException
import hero.model.Community
import hero.model.ImageAsset
import hero.sql.jooq.Tables.COMMUNITY
import org.jooq.DSLContext
import org.jooq.Record
import org.jooq.exception.NoDataFoundException
import java.util.UUID

class CommunityRepository(
    lazyContext: Lazy<DSLContext>,
) {
    private val context: DSLContext by lazyContext

    constructor(context: DSLContext) : this(lazy { context })

    fun findByOwnerId(ownerId: String): List<Community> {
        return context.select(JooqCommunityHelper.communityFields)
            .from(COMMUNITY)
            .where(COMMUNITY.OWNER_ID.eq(ownerId))
            .fetch()
            .map { JooqCommunityHelper.mapRecordToEntity(it) }
    }

    fun getById(communityId: UUID): Community {
        return context.select(JooqCommunityHelper.communityFields)
            .from(COMMUNITY)
            .where(COMMUNITY.ID.eq(communityId))
            .runCatching { JooqCommunityHelper.mapRecordToEntity(fetchSingle()) }
            .getOrElse {
                when (it) {
                    is NoDataFoundException -> throw NotFoundException()
                    else -> throw it
                }
            }
    }

    fun findById(communityId: UUID): Community? {
        return context.select(JooqCommunityHelper.communityFields)
            .from(COMMUNITY)
            .where(COMMUNITY.ID.eq(communityId))
            .fetchOne()
            ?.let { JooqCommunityHelper.mapRecordToEntity(it) }
    }

    fun save(community: Community): Community {
        context.insertInto(COMMUNITY)
            .set(COMMUNITY.ID, community.id)
            .set(COMMUNITY.NAME, community.name)
            .set(COMMUNITY.DESCRIPTION, community.description)
            .set(COMMUNITY.SLUG, community.slug)
            .set(COMMUNITY.OWNER_ID, community.ownerId)
            .set(COMMUNITY.MEMBERS_COUNT, community.membersCount)
            .set(COMMUNITY.PROFILE_IMAGE_URL, community.image?.id)
            .set(COMMUNITY.PROFILE_IMAGE_HEIGHT, community.image?.height)
            .set(COMMUNITY.PROFILE_IMAGE_WIDTH, community.image?.width)
            .set(COMMUNITY.CREATED_AT, community.createdAt)
            .set(COMMUNITY.UPDATED_AT, community.updatedAt)
            .set(COMMUNITY.DELETED_AT, community.deletedAt)
            .execute()

        return community
    }
}

object JooqCommunityHelper {
    fun mapRecordToEntity(record: Record): Community {
        val profileImage = if (record[COMMUNITY.PROFILE_IMAGE_URL] != null) {
            ImageAsset(
                id = record[COMMUNITY.PROFILE_IMAGE_URL],
                width = record[COMMUNITY.PROFILE_IMAGE_WIDTH],
                height = record[COMMUNITY.PROFILE_IMAGE_HEIGHT],
            )
        } else {
            null
        }

        return Community(
            id = record[COMMUNITY.ID],
            name = record[COMMUNITY.NAME],
            description = record[COMMUNITY.DESCRIPTION],
            slug = record[COMMUNITY.SLUG],
            ownerId = record[COMMUNITY.OWNER_ID],
            membersCount = record[COMMUNITY.MEMBERS_COUNT],
            image = profileImage,
            createdAt = record[COMMUNITY.CREATED_AT],
            updatedAt = record[COMMUNITY.UPDATED_AT],
            deletedAt = record[COMMUNITY.DELETED_AT],
        )
    }

    val communityFields = listOf(
        COMMUNITY.ID,
        COMMUNITY.NAME,
        COMMUNITY.DESCRIPTION,
        COMMUNITY.SLUG,
        COMMUNITY.OWNER_ID,
        COMMUNITY.MEMBERS_COUNT,
        COMMUNITY.PROFILE_IMAGE_URL,
        COMMUNITY.PROFILE_IMAGE_HEIGHT,
        COMMUNITY.PROFILE_IMAGE_WIDTH,
        COMMUNITY.CREATED_AT,
        COMMUNITY.UPDATED_AT,
        COMMUNITY.DELETED_AT,
    )
}
