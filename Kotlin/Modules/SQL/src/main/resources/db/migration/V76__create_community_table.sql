CREATE TABLE community
(
    id                   UUID PRIMARY KEY,
    name                 TEXT        NOT NULL,
    description          TEXT        NOT NULL,
    slug                 TEXT        NOT NULL,
    owner_id             TEXT        NOT NULL,
    members_count        BIGINT      NOT NULL,
    profile_image_url    TEXT        NULL,
    profile_image_height INT         NULL,
    profile_image_width  INT         NULL,
    created_at           TIMESTAMPTZ NOT NULL,
    updated_at           TIMESTAMPTZ NOT NULL,
    deleted_at           TIMESTAMPTZ NULL,

    CONSTRAINT "67c16a1f5cda405aa61171e3b7921627_fk" FOREIGN KEY (owner_id) REFERENCES "user" (id)
);

CREATE UNIQUE INDEX "42941e5cf80c493e8f6467eb691ca5cc_ux" ON community (slug) WHERE deleted_at IS NULL;

CREATE INDEX "e9607f04e487440f849a95c6074d5f91_ix" ON community (owner_id);

CREATE TABLE community_member
(
    id           BIGINT PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY,
    community_id UUID        NOT NULL,
    user_id      TEXT        NOT NULL,
    joined_at    TIMESTAMPTZ NOT NULL,
    kicked_at    TIMESTAMPTZ NULL,
    left_at      TIMESTAMPTZ NULL,
    state        TEXT        NOT NULL,
    updated_at   TIMESTAMPTZ NOT NULL
);

CREATE INDEX "f66c666c59c44a07a3b2b6754e410bb7_ix" on community_member (community_id, state);
CREATE INDEX "a06c408bfd0c48379b5023ac8ad7e0b5_ix" on community_member (user_id);
CREATE INDEX "d6468f062aed4b04841e797abf67bebb_ix" on community_member (state);
CREATE UNIQUE INDEX "25093817e2654062a773bfa3cc6e4d9f_ux" on community_member (community_id, user_id);

DO
$$
    BEGIN
        IF EXISTS (SELECT 1 FROM pg_user WHERE usename = '<EMAIL>') THEN
            GRANT ALL ON TABLE community TO "<EMAIL>";
            GRANT ALL ON TABLE community_member TO "<EMAIL>";
        END IF;
    END
$$;
