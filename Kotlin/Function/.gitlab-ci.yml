variables:
  FIRESTORE_TRIGGER: "--trigger-location=eur3 --trigger-event-filters=type=google.cloud.firestore.document.v1.written --trigger-event-filters=database=(default) --trigger-event-filters-path-pattern=document"
include:
  # deps
  - "/Kotlin/Function/Subscriber/.gitlab-ci.yml"
  # functions
  - "/Kotlin/Function/Accounting/.gitlab-ci.yml"
  - "/Kotlin/Function/BigQueryWriter/.gitlab-ci.yml"
  - "/Kotlin/Function/Community/.gitlab-ci.yml"
  - "/Kotlin/Function/CreatorSubscribersReportGenerator/.gitlab-ci.yml"
  - "/Kotlin/Function/Discord/.gitlab-ci.yml"
  - "/Kotlin/Function/GiftedSubscriptionExpirationNotifier/.gitlab-ci.yml"
  - "/Kotlin/Function/GjirafaLivestreamEnabler/.gitlab-ci.yml"
  - "/Kotlin/Function/GoogleUrlNotifier/.gitlab-ci.yml"
  - "/Kotlin/Function/MailgunPublisher/.gitlab-ci.yml"
  - "/Kotlin/Function/NotificationDisabler/.gitlab-ci.yml"
  - "/Kotlin/Function/Post/.gitlab-ci.yml"
  - "/Kotlin/Function/PostgresMigration/.gitlab-ci.yml"
  - "/Kotlin/Function/ProductFeedGenerator/.gitlab-ci.yml"
  - "/Kotlin/Function/Rss/.gitlab-ci.yml"
  - "/Kotlin/Function/SitemapGenerator/.gitlab-ci.yml"
  - "/Kotlin/Function/Statistics/.gitlab-ci.yml"
  - "/Kotlin/Function/Stripe/.gitlab-ci.yml"
  - "/Kotlin/Function/SubscriptionCounter/.gitlab-ci.yml"
  - "/Kotlin/Function/SubscriptionMessageThreadHandler/.gitlab-ci.yml"
  - "/Kotlin/Function/********************/.gitlab-ci.yml"
  - "/Kotlin/Function/SubscriptionSavedPostsHandler/.gitlab-ci.yml"
  - "/Kotlin/Function/SubscriptionSubscribeRequestsHandler/.gitlab-ci.yml"
  - "/Kotlin/Function/SubscriptionWatchActivityHandler/.gitlab-ci.yml"
  - "/Kotlin/Function/UnreadDirectMessageNotifier/.gitlab-ci.yml"
  - "/Kotlin/Function/UploadEventHandler/.gitlab-ci.yml"
  - "/Kotlin/Function/*********************/.gitlab-ci.yml"
  - "/Kotlin/Function/User/.gitlab-ci.yml"

.Kotlin/Function/deploy:
  needs:
    - Kotlin/BaseUtils/build
    - Kotlin/GoogleCloud/build
    - Kotlin/Model/build
    - Kotlin/Testing/build
  # we don't put function names to environments to prevent spamming
  # environment: $FUNCTION_NAME/$ENVIRONMENT
  script:
    - MODULE_HASH=$($CI_PROJECT_DIR/GitLab/module-hash.py $CI_JOB_NAME .gitlab-ci.yml Kotlin/Function/.gitlab-ci.yml)
    - CURRENT_HASH=$(gcloud functions describe $ENVIRONMENT_FUNCTION_NAME --region=europe-west1 --format='value(environmentVariables.MODULE_HASH)' || echo 1)
    - if [ "$CURRENT_HASH" == "$MODULE_HASH" ]; then
        echo "Function $ENVIRONMENT_FUNCTION_NAME/$MODULE_HASH is already deployed, skipping deployment.";
        gcloud functions list --regions=$CLOUD_REGION;
        exit;
      else
        echo "Replacing function of $CURRENT_HASH with $MODULE_HASH.";
      fi
    - $CI_PROJECT_DIR/GitLab/restore-dependencies.py $CI_JOB_NAME
    - ARTIFACT_NAME=$(basename "${PWD,,}")
    # deploy from jar: https://cloud.google.com/functions/docs/concepts/java-deploy
    # gen2 functions will require deletion of all previously running services
    # WARN runtime java21 is only supported withing gen2 functions
    - gcloud beta functions deploy $ENVIRONMENT_FUNCTION_NAME $TRIGGER
        --set-env-vars=$ENVIRONMENT_ENV_VARS
        --set-env-vars=MODULE_HASH=$MODULE_HASH
        --set-env-vars=SERVICE_NAME=$FUNCTION_NAME
        --set-env-vars=SERVICE_TYPE=cloud_function
        --set-env-vars=LOG_APPENDER=ConsoleFluentD
        --set-env-vars=SENTRY_DSN=$SENTRY_DSN
        --set-env-vars=SQL_DATABASE_NAME=$SQL_DATABASE_NAME
        --set-env-vars=SQL_INSTANCE_NAME=$SQL_INSTANCE_NAME
        --set-env-vars=INTERNAL_API_KEY=$INTERNAL_API_KEY
        --set-env-vars=GJIRAFA_PROJECT=$GJIRAFA_PROJECT
        --set-env-vars=GJIRAFA_API_KEY="$GJIRAFA_API_KEY"
        --set-env-vars=^##^GJIRAFA_IMAGE_KEY="$GJIRAFA_IMAGE_KEY"
        --set-env-vars=FLEXIBEE_PASSWORD=$FLEXIBEE_PASSWORD
        --set-env-vars=STRIPE_API_KEY_EU=$STRIPE_API_KEY_EU
        --set-env-vars=STRIPE_API_KEY_US=$STRIPE_API_KEY_US        
        --set-env-vars=$ENV_VARS
        --region europe-west1
        --memory $MEMORY
        --runtime java21
        --entry-point $CLASS_NAME
        --source $CI_PROJECT_DIR/m2/hero/${ARTIFACT_NAME}-shadow/ci/
        --timeout $TIMEOUT
        --max-instances $MAX_INSTANCES
        --service-account $SERVICE_ACCOUNT
        --clear-vpc-connector
        --ingress-settings all
        --allow-unauthenticated
        $ADDITIONAL_PARAMETERS
