Kotlin/Function/Community/CommunityCreatedHandler/build:
  stage: build-services
  needs:
    - Kotlin/Function/Subscriber/build
    - Kotlin/Modules/SQL/build
    - Kotlin/Modules/IntegrationTesting/build
  extends:
    - .Kotlin/job-build-gradle-module
    - .Function/variables-build

.Kotlin/Function/Community/CommunityCreatedHandler/variables:
  variables:
    FUNCTION_NAME: "community-created-handler"
    CLASS_NAME: "hero.functions.CommunityCreatedHandler"
    TOPIC: "CommunityCreated"

Kotlin/Function/Community/CommunityCreatedHandler/deploy-devel:
  needs:
    - Kotlin/Function/Community/CommunityCreatedHandler/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-devel
    - .Kotlin/Function/Community/CommunityCreatedHandler/variables

Kotlin/Function/Community/CommunityCreatedHandler/deploy-staging:
  needs:
    - Kotlin/Function/Community/CommunityCreatedHandler/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-staging
    - .Kotlin/Function/Community/CommunityCreatedHandler/variables

Kotlin/Function/Community/CommunityCreatedHandler/deploy-prod:
  needs:
    - Kotlin/Function/Community/CommunityCreatedHandler/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-prod
    - .Kotlin/Function/Community/CommunityCreatedHandler/variables
