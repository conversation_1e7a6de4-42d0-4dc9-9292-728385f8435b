package hero.functions

import hero.model.CommunityMemberStatus
import hero.model.topics.CommunityCreated
import hero.sql.jooq.Tables.COMMUNITY
import hero.sql.jooq.Tables.COMMUNITY_MEMBER
import hero.sql.jooq.Tables.USER
import hero.test.IntegrationTest
import hero.test.TestEnvironmentVariables
import hero.test.TestRepositories
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class CommunityCreatedHandlerIT : IntegrationTest() {
    @Test
    fun `should add all users that are subscribed to the creator to the community`() {
        val underTest = CommunityCreatedHandler(
            lazyTestContext,
            TestEnvironmentVariables,
            TestRepositories.communityRepository,
        )
        testHelper.createUser("cestmir")
        val community = testHelper.createCommunity("cestmir", membersCount = 0)
        testHelper.createUser("filip")
        testHelper.createSubscription(userId = "filip", creatorId = "cestmir")
        testHelper.createUser("petr")
        testHelper.createSubscription(userId = "petr", creatorId = "cestmir")

        underTest.consume(CommunityCreated(community.id))

        val communityMembers = testContext.selectFrom(COMMUNITY_MEMBER).fetch()
        assertThat(communityMembers).hasSize(2)
        with(communityMembers.first { it.userId == "filip" }) {
            assertThat(userId).isEqualTo("filip")
            assertThat(state).isEqualTo(CommunityMemberStatus.ACTIVE.name)
            assertThat(communityId).isEqualTo(community.id)
        }
        with(communityMembers.first { it.userId == "petr" }) {
            assertThat(userId).isEqualTo("petr")
            assertThat(state).isEqualTo(CommunityMemberStatus.ACTIVE.name)
            assertThat(communityId).isEqualTo(community.id)
        }
        assertThat(testContext.selectFrom(COMMUNITY).fetchSingle().membersCount).isEqualTo(2)

        assertThat(testContext.selectFrom(USER).where(USER.ID.eq("filip")).fetchSingle().joinedCommunitiesCount)
            .isEqualTo(1)
        assertThat(testContext.selectFrom(USER).where(USER.ID.eq("petr")).fetchSingle().joinedCommunitiesCount)
            .isEqualTo(1)
        assertThat(testContext.selectFrom(USER).where(USER.ID.eq("cestmir")).fetchSingle().joinedCommunitiesCount)
            .isEqualTo(0)
    }
}
