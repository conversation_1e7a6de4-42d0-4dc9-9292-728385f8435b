# CommunityCreatedHandler

A Cloud Function that automatically adds existing subscribers to newly created communities.

## Purpose

Handles `CommunityCreated` events to populate new communities with active subscribers, ensuring existing supporters become community members immediately upon creation.

## Functionality

- Listens to `CommunityCreated` Pub/Sub topic
- Retrieves community details using the provided community ID
- Cleans any existing community members (defensive cleanup)
- Bulk inserts all active subscribers of the community owner as `ACTIVE` members

## Key Components

- **Input**: `CommunityCreated` payload with `communityId`
- **Processing**: Fetches community data, queries active subscriptions, performs database operations
- **Output**: Updates `community_member` table with new memberships

## Dependencies

- SQL database (community and subscription data via JooqSQL)
- `CommunityRepository` for community data access
- `JooqSubscriptionHelper.activeSubscription` for filtering active subscriptions

## Deployment

- **Function Name**: `community-created-handler`
- **Topic**: `CommunityCreated`
- **Environments**: Development and Production via GitLab CI
