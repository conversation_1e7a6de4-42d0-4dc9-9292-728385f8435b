Kotlin/Function/SubscriptionMessageThreadHandler/build:
  stage: build-services
  needs:
    - Kotlin/Function/Subscriber/build
    - Kotlin/Modules/SQL/build
    - Kotlin/Modules/IntegrationTesting/build
  extends:
    - .Kotlin/job-build-gradle-module
    - .Function/variables-build

.Kotlin/Function/SubscriptionMessageThreadHandler/variables:
  variables:
    FUNCTION_NAME: "subscription-message-thread-handler"
    CLASS_NAME: "hero.functions.SubscriptionMessageThreadHandler"
    TOPIC: "SubscriberStatusChanged"

Kotlin/Function/SubscriptionMessageThreadHandler/deploy-devel:
  needs:
    - Kotlin/Function/SubscriptionMessageThreadHandler/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-devel
    - .Kotlin/Function/SubscriptionMessageThreadHandler/variables

Kotlin/Function/SubscriptionMessageThreadHandler/deploy-prod:
  needs:
    - Kotlin/Function/SubscriptionMessageThreadHandler/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-prod
    - .Kotlin/Function/SubscriptionMessageThreadHandler/variables
