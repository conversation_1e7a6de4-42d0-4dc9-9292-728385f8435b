# SubscriptionCommunityHandler

A Cloud Function that manages community memberships when subscription status changes.

## Purpose

Handles `SubscriberStatusChanged` events to automatically add or remove users from communities based on their subscription status with community owners.

## Functionality

- Listens to `SubscriberStatusChanged` Pub/Sub topic
- **On UNSUBSCRIBED**: Finds all communities owned by the creator where the user is an active member and marks them as `SUBSCRIPTION_EXPIRED`
- **On SUBSCRIBED**: Adds the user to all active (non-deleted) communities owned by the creator
- Only affects communities owned by the specific creator in the event

## Key Components

- **Input**: `SubscriberStatusChanged` payload with `userId`, `creatorId`, and `statusChange`
- **Processing**: Queries communities and community memberships, updates membership status
- **Output**: Updates `community_member` table with new membership states

## Dependencies

- SQL database (community and community_member tables via JooqSQL)
- `CommunityMemberStatus` enum for membership states
- `Tables.COMMUNITY` and `Tables.COMMUNITY_MEMBER` for database operations

## Deployment

- **Function Name**: `subscription-community-handler`
- **Topic**: `SubscriberStatusChanged`
- **Environments**: Development and Production via GitLab CI
