package hero.functions

import com.stripe.model.Transfer
import com.stripe.param.TransferCreateParams
import hero.gcloud.typedCollectionOf
import hero.model.AppleCharge
import hero.model.Currency
import hero.model.Subscriber
import hero.model.Tier
import hero.model.topics.Hourly
import hero.test.IntegrationTest
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.spyk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test
import java.time.Instant

class StripeAppleTransferrerIT : IntegrationTest() {
    private val appleTransfersCollection = firestore.typedCollectionOf(AppleCharge)
    private val underTest = spyk(StripeAppleTransferrer(production = false, firestore = firestore))

    @Test
    fun `create corresponding Stripe transfer`() {
        val appleTransfer = AppleCharge(
            appleTransactionId = "apple_txn_123",
            appleReferenceId = "apple_ref_456",
            createdAt = Instant.now(),
            userId = "user_123",
            creatorId = "creator_456",
            tierId = "EUR05",
            targetAccountId = "acct_test_account",
            stripeTransferId = null, // initially null - this should be updated after transfer
            transferredAt = null, // initially null - this should be updated after transfer
            currencyStore = Currency.CZK,
            conversionRateCents = 2525,
            priceStoreCents = 14900,
            priceFeeHeroheroCents = 2300,
        )

        val tier = Tier.ofId(appleTransfer.tierId)

        val entity = appleTransfersCollection[appleTransfer.appleTransactionId]
        entity.set(appleTransfer)

        val mockStripeTransfer = mockk<Transfer>().apply {
            every { id } returns "tr_mock_stripe_transfer_123"
            every { currency } returns tier.currency.name.lowercase()
            every { amount } returns tier.feeAbsolute.movePointRight(3).toLong()
        }

        // capture the parameters passed to createStripeTransfer
        val transferParamsSlot = slot<TransferCreateParams>()

        every { underTest.createStripeTransfer(tier.currency, capture(transferParamsSlot)) } returns mockStripeTransfer

        underTest.consume(Hourly())

        // verify the mock was called
        verify(exactly = 1) { underTest.createStripeTransfer(tier.currency, any()) }

        // verify the TransferCreateParams contains all required fields
        val capturedParams = transferParamsSlot.captured

        assertEquals(tier.feeAbsolute.movePointRight(3).toLong(), capturedParams.amount)
        assertEquals(tier.currency.name.lowercase(), capturedParams.currency)
        assertEquals(appleTransfer.targetAccountId, capturedParams.destination)
        assertEquals(appleTransfer.appleReferenceId, capturedParams.transferGroup)

        // verify metadata contains all required fields
        val metadata = capturedParams.metadata
        assertEquals(appleTransfer.appleReferenceId, metadata[Subscriber::appleReferenceId.name])
        assertEquals(appleTransfer.appleTransactionId, metadata[Subscriber::appleTransactionId.name])
        assertEquals(appleTransfer.userId, metadata[Subscriber::userId.name])
        assertEquals(appleTransfer.creatorId, metadata[Subscriber::creatorId.name])

        // verify the firestore object was modified
        val updatedTransfer = entity.fetch()
        assertNotNull(updatedTransfer)
        assertEquals(mockStripeTransfer.id, updatedTransfer!!.stripeTransferId)
        assertNotNull(updatedTransfer.transferredAt)

        // now try to perform the transfer again and simulate that firstore fields were not updated,
        // it should still be no-op in terms of stripe transfers
        entity.field(AppleCharge::stripeTransferId).update(null)
        entity.field(AppleCharge::transferredAt).update(null)

        every {
            underTest.fetchStripeTransfer(
                tier.currency,
                appleTransfer.appleReferenceId,
                appleTransfer.appleTransactionId,
            )
        } returns mockStripeTransfer

        underTest.consume(Hourly())
        // stripe transfer was only called once before, it should not be called again (1 total)
        verify(exactly = 1) { underTest.createStripeTransfer(tier.currency, any()) }

        // but the firestore entity must be updated
        val updatedTransferAgain = entity.fetch()
        assertNotNull(updatedTransferAgain)
        assertEquals(mockStripeTransfer.id, updatedTransferAgain!!.stripeTransferId)
        assertNotNull(updatedTransferAgain.transferredAt)

        verify(exactly = 2) { underTest.updateStripeTransfer(appleTransfer.appleTransactionId, any()) }
    }
}
