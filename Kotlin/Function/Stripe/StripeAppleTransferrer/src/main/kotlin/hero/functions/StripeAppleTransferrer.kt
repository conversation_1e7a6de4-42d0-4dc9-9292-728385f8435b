package hero.functions

import com.stripe.exception.InvalidRequestException
import com.stripe.model.Transfer
import com.stripe.param.TransferCreateParams
import com.stripe.param.TransferListParams
import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.exceptions.http.ConflictException
import hero.gcloud.FirestoreRef
import hero.gcloud.TypedCollectionReference
import hero.gcloud.firestore
import hero.gcloud.get
import hero.gcloud.isNull
import hero.gcloud.typedCollectionOf
import hero.gcloud.where
import hero.model.AppleCharge
import hero.model.Currency
import hero.model.Subscriber
import hero.model.Tier
import hero.model.topics.Hourly
import hero.stripe.model.StripeKeys
import hero.stripe.service.StripeClients
import java.time.Instant

@Suppress("unused")
class StripeAppleTransferrer(
    production: Boolean = SystemEnv.isProduction,
    firestore: FirestoreRef = firestore(SystemEnv.cloudProject, production),
) : PubSubSubscriber<Hourly>() {
    private val stripeKeysRepository = TypedCollectionReference<StripeKeys>(firestore.firestore["constants"])
    private val stripeClients = StripeClients(SystemEnv.stripeKeyEu, SystemEnv.stripeKeyUs)
    private val appleTransfersCollection = firestore.typedCollectionOf(AppleCharge)

    override fun consume(payload: Hourly) {
        fetchPendingTransfers()
            .forEach { transfer ->
                val tier = Tier.ofId(transfer.tierId)
                val logContext = createLogContext(transfer)

                // validate the transfer does not exist yet and be idempotent
                fetchStripeTransfer(tier.currency, transfer.appleReferenceId, transfer.appleTransactionId)
                    ?.let {
                        log.info(
                            "Transfer ${transfer.appleTransactionId} already exists, skipping",
                            logContext,
                        )
                        updateStripeTransfer(transfer.appleTransactionId, it.id)
                        return@forEach
                    }

                val stripeTransfer = createStripeTransfer(tier.currency, buildTransferParams(transfer, tier))

                log.info(
                    "Transferring ${stripeTransfer.currency} ${stripeTransfer.amount}" +
                        " of ${transfer.appleTransactionId} to ${transfer.targetAccountId}",
                    logContext,
                )

                updateStripeTransfer(transfer.appleTransactionId, stripeTransfer.id)
            }
    }

    private fun buildTransferParams(
        transfer: AppleCharge,
        tier: Tier,
    ): TransferCreateParams =
        TransferCreateParams.builder()
            // TODO compute correct pricing
            .setAmount(tier.feeAbsolute.movePointRight(3).toLong())
            .setCurrency(tier.currency.name.lowercase())
            .putMetadata(Subscriber::appleReferenceId.name, transfer.appleReferenceId)
            .putMetadata(Subscriber::appleTransactionId.name, transfer.appleTransactionId)
            .putMetadata(Subscriber::userId.name, transfer.userId)
            .putMetadata(Subscriber::creatorId.name, transfer.creatorId)
            .putMetadata(Subscriber::tierId.name, transfer.tierId)
            .putMetadata(AppleCharge::createdAt.name, transfer.createdAt.toString())
            .setDestination(transfer.targetAccountId)
            .setTransferGroup(transfer.appleReferenceId)
            .build()

    private fun createLogContext(transfer: AppleCharge): Map<String, String> =
        mapOf(
            "userId" to transfer.userId,
            "creatorId" to transfer.creatorId,
            "appleTransactionId" to transfer.appleTransactionId,
            "appleReferenceId" to transfer.appleReferenceId,
        )

    internal fun updateStripeTransfer(
        appleTransactionId: String,
        stripeTransferId: String,
    ) {
        val entity = appleTransfersCollection[appleTransactionId]
        entity.field(AppleCharge::stripeTransferId).update(stripeTransferId)
        entity.field(AppleCharge::transferredAt).update(Instant.now())
    }

    internal fun createStripeTransfer(
        currency: Currency,
        transfer: TransferCreateParams,
    ): Transfer =
        try {
            stripeClients[currency].transfers().create(transfer)
        } catch (e: InvalidRequestException) {
            if (e.code == "balance_insufficient") {
                val balance = try {
                    stripeClients[currency].balance().retrieve()
                } catch (e: Exception) {
                    log.error("Cannot fetch current balance: ${e.message}", cause = e)
                    null
                }
                throw ConflictException(
                    "Insufficient balance to perform Apple->Stripe transfer ${transfer.metadata}" +
                        " (will be retried), available was ${balance?.available}",
                    transfer.metadata,
                )
            }
            throw e
        }

    internal fun fetchStripeTransfer(
        currency: Currency,
        appleReferenceId: String,
        appleTransactionId: String,
    ): Transfer? =
        stripeClients[currency].transfers()
            .list(TransferListParams.builder().setTransferGroup(appleReferenceId).build())
            .autoPagingIterable()
            .firstOrNull { it.metadata[Subscriber::appleTransactionId.name] == appleTransactionId }

    internal fun fetchPendingTransfers(): List<AppleCharge> =
        appleTransfersCollection
            .where(AppleCharge::stripeTransferId).isNull()
            .fetchAll()
}
