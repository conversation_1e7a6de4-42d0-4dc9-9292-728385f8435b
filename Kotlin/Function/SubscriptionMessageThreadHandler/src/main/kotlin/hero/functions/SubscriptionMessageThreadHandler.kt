package hero.functions

import hero.baseutils.EnvironmentVariables
import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.core.logging.Logger
import hero.gcloud.TypedCollectionReference
import hero.gcloud.contains
import hero.gcloud.firestore
import hero.gcloud.typedCollectionOf
import hero.gcloud.where
import hero.model.MessageThread
import hero.model.topics.SubscriberStatusChanged
import hero.repository.subscription.canMessage
import hero.sql.ConnectorConnectionPool
import hero.sql.jooq.JooqSQL
import org.jooq.DSLContext

@Suppress("Unused")
class SubscriptionMessageThreadHandler(
    lazyContext: Lazy<DSLContext> = lazy { JooqSQL.context(ConnectorConnectionPool.dataSource) },
    systemEnv: EnvironmentVariables = SystemEnv,
    private val messageThreadsCollection: TypedCollectionReference<MessageThread> = firestore(
        SystemEnv.cloudProject,
        systemEnv.isProduction,
    ).typedCollectionOf(MessageThread),
    private val logger: Logger = log,
) : PubSubSubscriber<SubscriberStatusChanged>(systemEnv) {
    private val context: DSLContext by lazyContext

    override fun consume(payload: SubscriberStatusChanged) {
        logger.info("Updating message thread for user ${payload.userId}")
        val userId = payload.userId
        val messageThreads = messageThreadsCollection.where(MessageThread::userIds).contains(userId).fetchAll()
        messageThreads.forEach {
            val targetUserId = it.userIds.firstOrNull { it != userId }
            if (targetUserId != null) {
                val canMessage = canMessage(context, userId, targetUserId)
                if (it.canMessage.values.first() != canMessage) {
                    val canMessageMap = it.userIds.associate { it to canMessage }
                    logger.info("Updating message thread ${it.id}, setting can message to $canMessageMap")
                    messageThreadsCollection[it.id].field(MessageThread::canMessage).update(canMessageMap)
                }
            }
        }
    }
}
