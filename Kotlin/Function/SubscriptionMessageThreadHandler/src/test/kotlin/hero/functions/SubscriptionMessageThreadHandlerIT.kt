package hero.functions

import hero.model.SubscriberStatus
import hero.model.topics.SubscriberStatusChange
import hero.model.topics.SubscriberStatusChanged
import hero.test.IntegrationTest
import hero.test.IntegrationTestHelper.TestCollections
import hero.test.TestEnvironmentVariables
import hero.test.logging.TestLogger
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class SubscriptionMessageThreadHandlerIT : IntegrationTest() {
    @Test
    fun `should update all of message threads that user is part of`() {
        val underTest = SubscriptionMessageThreadHandler(
            lazyTestContext,
            TestEnvironmentVariables,
            TestCollections.messageThreadsCollection,
            TestLogger,
        )

        testHelper.createSubscription(userId = "filip", creatorId = "cestmir")
        val messageThread1 = testHelper.createMessageThread(
            "cestmir",
            listOf("filip"),
            canMessage = mapOf("cestmir" to true, "filip" to true),
        )

        testHelper.createSubscription(userId = "pavel", creatorId = "cestmir")
        val messageThread2 = testHelper.createMessageThread(
            "cestmir",
            listOf("pavel"),
            canMessage = mapOf("cestmir" to true, "pavel" to true),
        )

        testHelper.createSubscription(userId = "igor", creatorId = "cestmir", status = SubscriberStatus.INCOMPLETE)
        val messageThread3 = testHelper.createMessageThread(
            "cestmir",
            listOf("igor"),
            canMessage = mapOf("cestmir" to true, "igor" to true),
        )

        underTest.consume(
            SubscriberStatusChanged(
                "cestmir",
                "someone-else",
                SubscriberStatusChange.SUBSCRIBED,
                false,
                "",
                false,
                false,
                false,
            ),
        )
        assertThat(TestCollections.messageThreadsCollection[messageThread1.id].get().canMessage).isEqualTo(
            mapOf(
                "cestmir" to true,
                "filip" to true,
            ),
        )

        assertThat(TestCollections.messageThreadsCollection[messageThread2.id].get().canMessage).isEqualTo(
            mapOf(
                "cestmir" to true,
                "pavel" to true,
            ),
        )

        assertThat(TestCollections.messageThreadsCollection[messageThread3.id].get().canMessage).isEqualTo(
            mapOf(
                "cestmir" to false,
                "igor" to false,
            ),
        )
    }
}
