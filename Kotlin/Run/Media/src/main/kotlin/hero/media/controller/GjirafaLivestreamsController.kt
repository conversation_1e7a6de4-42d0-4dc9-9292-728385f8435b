package hero.media.controller

import hero.baseutils.FuelException
import hero.baseutils.log
import hero.core.data.Caching
import hero.exceptions.http.BadRequestException
import hero.exceptions.http.ForbiddenException
import hero.exceptions.http.NotFoundException
import hero.gcloud.TypedCollectionReference
import hero.gcloud.contains
import hero.gcloud.where
import hero.gjirafa.GjirafaLivestreamsService
import hero.gjirafa.GjirafaStatsService
import hero.gjirafa.dto.CutLivestreamRequest
import hero.gjirafa.dto.LiveVideoResponseV2Minimal
import hero.gjirafa.dto.PostLiveVideoRequest
import hero.gjirafa.dto.PutLiveVideoRequest
import hero.gjirafa.dto.StopAndCutRequest
import hero.gjirafa.dto.exampleGjirafaChannel
import hero.gjirafa.dto.toDomainStatus
import hero.http4k.auth.getJwtUser
import hero.http4k.extensions.body
import hero.http4k.extensions.caching
import hero.http4k.extensions.delete
import hero.http4k.extensions.example
import hero.http4k.extensions.get
import hero.http4k.extensions.lens
import hero.http4k.extensions.post
import hero.http4k.extensions.put
import hero.jackson.fromJson
import hero.media.controller.dto.GjirafaEvent
import hero.media.controller.dto.GjirafaLivestreamEventData
import hero.media.controller.dto.GjirafaProcessingUrl
import hero.media.controller.dto.exampleGjirafaCutRequest
import hero.media.controller.dto.exampleGjirafaCutResponse
import hero.media.controller.dto.exampleGjirafaLiveVideoResponse
import hero.media.controller.dto.exampleGjirafaStopAndCutRequest
import hero.media.controller.dto.exampleGjirafaVideoThumbnailResponse
import hero.media.controller.dto.exampleLiveVideoResponseV2Minimal
import hero.media.controller.dto.examplePostLiveVideoRequest
import hero.model.Post
import hero.model.User
import hero.model.topics.PostState
import org.http4k.contract.ContractRoute
import org.http4k.contract.div
import org.http4k.core.Response
import org.http4k.core.Status
import org.http4k.lens.Path
import org.http4k.lens.string
import java.time.Duration
import java.time.Instant
import java.time.temporal.ChronoUnit

class GjirafaLivestreamsController(
    private val livestreamsService: GjirafaLivestreamsService,
    private val statsService: GjirafaStatsService,
    private val usersCollection: TypedCollectionReference<User>,
    private val postsCollection: TypedCollectionReference<Post>,
) {
    @Suppress("Unused")
    val routePostNewLiveVideo: ContractRoute =
        ("/v1/gjirafa/live-streams").post(
            summary = "Create new live video",
            tag = "Gjirafa streams",
            parameters = object {},
            responses = listOf(Status.OK example exampleGjirafaLiveVideoResponse),
            receiving = examplePostLiveVideoRequest,
            handler = { request, _ ->
                val userId = request.getJwtUser().id
                val user = usersCollection[userId].get()
                val livestreamsMeta = user.gjirafaLivestream
                    ?: throw BadRequestException(
                        "User ${user.id} does not have Gjirafa streams enabled.",
                        mapOf("userId" to userId),
                    )
                // FE should not send author and channelId here
                // we should consider separating DTOs for our FE and when communicating with Gjirafa backends
                val body = lens<PostLiveVideoRequest>(request)
                    .copy(
                        title = "Stream of '${user.name}' on ${Instant.now().truncatedTo(ChronoUnit.SECONDS)}",
                        author = userId,
                        description = userId,
                        channelId = livestreamsMeta.publicId,
                    )
                val liveVideo = livestreamsService.postLiveVideo(body)
                log.info(
                    "User $userId is creating live video ${liveVideo.id}",
                    mapOf("assetId" to liveVideo.id, "userId" to userId),
                )
                Response(Status.OK).body(liveVideo)
            },
        )

    @Suppress("Unused")
    val routeGetMyChannel: ContractRoute =
        ("/v1/gjirafa/my-channel").get(
            summary = "Get details of my existing channel",
            tag = "Gjirafa streams",
            parameters = object {},
            responses = listOf(Status.OK example exampleGjirafaChannel),
            handler = { request, _ ->
                val userId = request.getJwtUser().id
                val user = usersCollection[userId].get()
                val channel = user.gjirafaLivestream?.publicId
                    ?: throw NotFoundException(
                        "User $userId does not have livestreams enabled.",
                        mapOf("userId" to userId),
                    )
                Response(Status.OK).body(livestreamsService.getChannel(channel))
            },
        )

    @Suppress("Unused")
    val routeGetLiveVideo: ContractRoute =
        ("/v1/gjirafa/live-streams/" / Path.string().of("videoId")).get(
            summary = "Get details of existing live video",
            tag = "Gjirafa streams",
            parameters = object {},
            responses = listOf(Status.OK example exampleLiveVideoResponseV2Minimal),
            handler = { _, _, videoId ->
                val video = livestreamsService.getLiveVideo(id = videoId)
                val liveViewerCount = statsService.liveViewers(video.id)
                Response(Status.OK)
                    // https://linear.app/herohero/issue/HH-3674/append-parameter-to-regular-livestream-check
                    .caching(Caching.Enabled(Duration.ofMinutes(1), public = true))
                    .body(
                        LiveVideoResponseV2Minimal(
                            videoId,
                            video.liveStatus,
                            video.startDateUTC,
                            liveViewerCount,
                        ),
                    )
            },
        )

    @Suppress("Unused")
    val routeGetLiveVideoDetails: ContractRoute =
        ("/v1/gjirafa/live-streams/" / Path.string().of("videoId") / "details").get(
            summary = "Get details of existing live video",
            tag = "Gjirafa streams",
            parameters = object {},
            responses = listOf(Status.OK example exampleGjirafaLiveVideoResponse),
            handler = { request, _, videoId, _ ->
                val userId = request.getJwtUser().id
                val isAuthor = livestreamsService.isAuthor(userId, videoId)

                if (!isAuthor) {
                    throw ForbiddenException("Only author of the stream can access it's details")
                }

                val video = livestreamsService.getLiveVideo(id = videoId)
                val liveViewerCount = statsService.liveViewers(video.id)
                Response(Status.OK)
                    .caching(Caching.Disabled)
                    .body(video.copy(viewerCount = liveViewerCount))
            },
        )

    @Suppress("Unused")
    val routePutExistingLiveVideo: ContractRoute =
        ("/v1/gjirafa/live-streams/" / Path.string().of("videoId")).put(
            summary = "Update existing live video",
            tag = "Gjirafa streams",
            parameters = object {},
            responses = listOf(Status.OK example exampleGjirafaLiveVideoResponse),
            receiving = mapOf("TODO" to "TODO"),
            handler = { request, _, videoId ->
                val userId = request.getJwtUser().id
                val body = lens<PutLiveVideoRequest>(request)
                log.info(
                    "User $userId is updating live video $videoId",
                    mapOf("assetId" to videoId, "userId" to userId),
                )
                livestreamsService.assertAuthor(userId, videoId)
                livestreamsService.putLiveVideo(videoId, body)
                Response(Status.OK)
            },
        )

    @Suppress("Unused")
    val routeDeleteLiveVideo: ContractRoute =
        ("/v1/gjirafa/live-streams/" / Path.string().of("videoId")).delete(
            summary = "Delete live video",
            tag = "Gjirafa streams",
            parameters = object {},
            responses = listOf(Status.NO_CONTENT example Unit),
            handler = { request, _, videoId ->
                val userId = request.getJwtUser().id
                log.info(
                    "User $userId is deleting live video $videoId",
                    mapOf("assetId" to videoId, "userId" to userId),
                )
                livestreamsService.assertAuthor(userId, videoId)
                livestreamsService.deleteLiveVideo(videoId)
                Response(Status.NO_CONTENT)
            },
        )

    @Suppress("Unused")
    val routePostLiveVideoThumbnail: ContractRoute =
        ("/v1/gjirafa/live-streams/" / Path.string().of("videoId") / "thumbnail").put(
            summary = "Upload live video thumbnail",
            tag = "Gjirafa streams",
            parameters = object {},
            responses = listOf(Status.OK example exampleGjirafaVideoThumbnailResponse),
            receiving = GjirafaProcessingUrl("https://thumbnail-url-to-store", 123456789L),
            handler = { request, _, videoId, _ ->
                val userId = request.getJwtUser().id
                val body = lens<GjirafaProcessingUrl>(request)
                livestreamsService.assertAuthor(userId, videoId)
                val thumbnail = livestreamsService.postVideoThumbnail(videoId, body.url)
                Response(Status.OK).body(thumbnail)
            },
        )

    @Suppress("Unused")
    val routePutGoLive: ContractRoute =
        ("/v1/gjirafa/live-streams/" / Path.string().of("videoId") / "go-live").put(
            summary = "Go live with given video",
            tag = "Gjirafa streams",
            parameters = object {},
            responses = listOf(Status.NO_CONTENT example Unit),
            receiving = null,
            handler = { request, _, videoId, _ ->
                val userId = request.getJwtUser().id
                livestreamsService.assertAuthor(userId, videoId)
                log.info(
                    "User $userId is going live with $videoId",
                    mapOf("assetId" to videoId, "userId" to userId),
                )
                try {
                    livestreamsService.goLive(videoId)
                } catch (e: FuelException) {
                    Response(Status.BAD_REQUEST).body(mapOf("message" to e.message))
                }
                Response(Status.NO_CONTENT)
            },
        )

    @Suppress("Unused")
    val routePostCutAndPublish: ContractRoute =
        ("/v1/gjirafa/live-streams/" / Path.string().of("videoId") / "cut-and-publish").post(
            summary = "Cut and publish given live video",
            tag = "Gjirafa streams",
            parameters = object {},
            responses = listOf(Status.OK example exampleGjirafaCutResponse),
            receiving = exampleGjirafaCutRequest,
            handler = { request, _, videoId, _ ->
                val userId = request.getJwtUser().id
                val cutRequest = lens<CutLivestreamRequest>(request)
                livestreamsService.assertAuthor(userId, videoId)
                log.info(
                    "User $userId is cut-and-publishing live video $videoId",
                    mapOf("assetId" to videoId, "userId" to userId),
                )
                val response = livestreamsService.cutAndPublish(videoId, cutRequest)
                Response(Status.OK).body(response)
            },
        )

    @Suppress("Unused")
    val routePostStopAndCut: ContractRoute =
        ("/v1/gjirafa/live-streams/" / Path.string().of("videoId") / "stop-and-cut").post(
            summary = "Stop and cut given live video",
            tag = "Gjirafa streams",
            parameters = object {},
            responses = listOf(Status.OK example exampleGjirafaCutResponse),
            receiving = exampleGjirafaStopAndCutRequest,
            handler = { request, _, videoId, _ ->
                val userId = request.getJwtUser().id
                val user = usersCollection[userId].get()
                val cutRequest = lens<StopAndCutRequest>(request).copy(hasDrm = user.hasDrm ?: false)
                livestreamsService.assertAuthor(userId, videoId)
                log.info(
                    "User $userId is stop-and-cutting live video $videoId",
                    mapOf("assetId" to videoId, "userId" to userId),
                )
                val response = livestreamsService.stopAndCut(videoId, cutRequest)
                Response(Status.OK).body(response)
            },
        )

    /**
     * Live video webhooks are currently used only for status tracking for future debugging.
     * Live video statuses are read directly from Gjirafa states and switching to regular videos
     * is done via Uploads webhooks.
     *
     * https://vp.gjirafa.tech/documentation/api/webhooks/liveVideo
     * https://client.vp.gjirafa.tech/agmipobm/webhooks
     */
    @Suppress("Unused")
    val routeWebhooks: ContractRoute =
        ("/v1/gjirafa/live-webhooks").post(
            summary = "Live video webhooks",
            tag = "Gjirafa streams",
            parameters = object {},
            responses = listOf(Status.NO_CONTENT to Unit),
            receiving = null,
            handler = { request, _ ->
                val body = String(request.body.payload.array())
                try {
                    // val event = lens<GjirafaEvent<GjirafaLivestreamEventData>(request)
                    val event = body.fromJson<GjirafaEvent<GjirafaLivestreamEventData>>()
                    val assetId = event.data.id
                    val liveStatus = event.data.status
                    if (event.eventType == "live.video.status.changed" && assetId != null && liveStatus != null) {
                        val posts = postsCollection
                            .where(Post::assetIds).contains(assetId)
                            .and(Post::state)
                            .isIn(listOf(PostState.PROCESSING, PostState.PUBLISHED, PostState.SCHEDULED))
                            .fetchAll()

                        posts
                            .map {
                                val updatedAssets = it.assets.map { asset ->
                                    val gjirafaLive = asset.gjirafaLive
                                    if (gjirafaLive?.id == assetId) {
                                        asset.copy(
                                            gjirafaLive = gjirafaLive.copy(
                                                liveStatus = liveStatus.toDomainStatus(),
                                            ),
                                        )
                                    } else {
                                        asset
                                    }
                                }
                                log.info("Updated from ${it.assets} to $updatedAssets")
                                it.copy(assets = updatedAssets)
                            }
                            .forEach {
                                postsCollection[it.id].set(it)
                            }
                    }
                    log.info("Received Gjirafa Livestream event: $body")
                } catch (e: Exception) {
                    log.fatal("Gjirafa Livestream webhook processing: ${e.message}: $body", cause = e)
                    throw e
                }
                Response(Status.NO_CONTENT)
            },
        )
}
