package hero.media.controller.dto

import hero.baseutils.instantOf
import hero.baseutils.minusHours
import hero.gjirafa.dto.AddAudioWatchStateRequest
import hero.gjirafa.dto.AddVideoWatchStateRequest
import hero.gjirafa.dto.CreateAudioAnalyticsEventRequest
import hero.gjirafa.dto.CreateVideoAnalyticsEventRequest
import hero.gjirafa.dto.CutLivestreamRequest
import hero.gjirafa.dto.CutLivestreamResponse
import hero.gjirafa.dto.GjirafaMultipartPart
import hero.gjirafa.dto.LiveVideoResponseV2
import hero.gjirafa.dto.LiveVideoResponseV2Minimal
import hero.gjirafa.dto.LiveVideoStatus
import hero.gjirafa.dto.PostLiveVideoRequest
import hero.gjirafa.dto.PostLiveVideoThumbnail
import hero.gjirafa.dto.StopAndCutRequest
import hero.model.GjirafaEncodeRequest
import hero.model.GjirafaPartToEncode
import hero.model.GjirafaPartUrl
import hero.model.GjirafaQualityType
import hero.model.GjirafaStatus
import hero.model.GjirafaUploadInfo
import hero.model.GjirafaUploadResponseResult
import java.time.Instant

val exampleGjirafaUploadResponseResult = GjirafaUploadResponseResult(
    url = "https://gjirafaupload/0123456789",
    requestKey = "0123456789",
    uploadFileDetailsId = 123456789L,
)

@Suppress("ktlint:standard:max-line-length")
val exampleGjirafaUploadInfo = GjirafaUploadInfo(
    uploadId = "DZsyGihHvnJjNJUPA",
    requestKey = "2006bb74361a26ab1bb0b",
    presignedUrl = listOf(
        GjirafaPartUrl(
            partNumber = 1,
            presignedUrl = "https://aaaaaaaa.blob.gjirafa.tech/uploads/153f94cf29/" +
                "file.mp4?uploadId=GihHvnJjNJUPA&partNumber=1",
        ),
        GjirafaPartUrl(
            partNumber = 2,
            presignedUrl = "https://aaaaaaaa.blob.gjirafa.tech/uploads/153f94cf29/" +
                "file.mp4?uploadId=GihHvnJjNJUPA&partNumber=2",
        ),
    ),
    uploadFileDetailsId = 123456789L,
)

val exampleGjirafaQualityType = GjirafaQualityType(
    qualityType = "480p",
    streamUrl = "https://cdn.vpplayer.tech/aaaaaaaa/encode/vjsnljny/hls/480p/index.m3u8",
    status = GjirafaStatus.COMPLETE,
    duration = 3.870833,
    size = 415339,
    width = 854,
    height = 480,
    bitrate = 842037,
    audioCodec = "mp4",
    audioBitrate = 0,
    audioSampleRate = 0,
    videoId = "vaaaaaaaa",
    audioId = "bcccccccc",
    progress = 100,
    startDateTime = Instant.now().minusHours(1),
    endDateTime = Instant.now(),
)

val exampleGjirafaEncodeRequest = GjirafaEncodeRequest(
    requestKey = "string",
    parts = listOf(
        GjirafaPartToEncode(
            partNumber = 0,
            eTag = "string",
        ),
    ),
)

val exampleGjirafaAssetId = GjirafaAssetId("kfkfmenakfnd")
val exampleGjirafaProcessingUrl = GjirafaProcessingUrl("https://url-to-be-stored-or-processed", 123456789L)

val exampleGjirafaMultipartPart = GjirafaMultipartPart(
    size = 512000,
    lastModified = instantOf("2023-05-16T13:00:42.442+02:00"),
    partNumber = 1,
    eTag = "\"cc1206727ffcab9a5d2af3ac18bfa048\"",
)

val exampleGjirafaMultipartPartsResponse = InternalGjirafaResponse(listOf(exampleGjirafaMultipartPart))
val exampleGjirafaMultipartAbort = InternalGjirafaResponse(true)

val exampleGjirafaAddVideoWatchStateRequest = AddVideoWatchStateRequest(
    uniqueViewId = "unique-view-id",
    userId = "user-id",
    videoId = "video-id",
    muted = true,
    currentTime = 100.0,
    segmentWatchTime = 100,
    volume = 100.0,
    fullScreen = true,
    eventType = "event-type",
    playerId = "player-id",
    playerState = true,
    playlistId = "playlist-id",
    insertTime = Instant.now(),
    userAgent = "Mozilla",
    duration = 100.1,
    isLive = true,
)

val exampleGjirafaAddAudioWatchStateRequest = AddAudioWatchStateRequest(
    uniqueViewId = "unique-view-id",
    userId = "user-id",
    audioId = "audio-id",
    playerId = "player-id",
    podcastId = "podcast-id",
    eventType = "watchState",
    muted = true,
    currentTime = 100.0,
    segmentWatchTime = 100,
    volume = 100.0,
    duration = 100.1,
)

val exampleGjirafaCreateVideoAnalyticsEventRequest = CreateVideoAnalyticsEventRequest(
    uniqueViewId = "unique-view-id",
    videoId = "video-id",
    adScheduleId = "ad-schedule",
    adId = "ad-id",
    userId = "user-id",
    playerId = "player-id",
    playlistId = "playlist-id",
    event = "event",
    countryCode = "CZ",
    operationSystem = "os",
    osVersion = "os-version",
    deviceBrand = "device-brand",
    deviceModel = "device-model",
    appIdentifier = "app",
    appVersion = "8.1",
    playerSdkVersion = "3.1",
    extraData = "extra-data",
    duration = 15.1,
    subtitles = true,
    isLive = true,
    userAgent = "geko",
    domain = "domain",
    userIp = "*******",
    url = "url",
)

val exampleGjirafaCreateAudioAnalyticsEventRequest = CreateAudioAnalyticsEventRequest(
    uniqueViewId = "unique-view-id",
    audioId = "audio-id",
    playerId = "player-id",
    userId = "user-id",
    podcastId = "podcast-id",
    event = "event",
    adId = "ad-id",
    countryCode = "CZ",
    browser = "browser",
    operatingSystem = "mac os",
    deviceType = "device type",
    duration = 15.1,
    userAgent = "geko",
    domain = "domain",
    userIp = "*******",
    url = "url",
)

val exampleGjirafaLiveVideoResponse = LiveVideoResponseV2(
    id = "67890fghij",
    title = "Amazing Live Video",
    description = "This is a sample live video description. Watch now!",
    playbackUrl = "https://example.com/live-video/67890fghij",
    thumbnail = "https://example.com/images/thumbnail.jpg",
    channelPublicId = "channel123",
    channelName = "ExampleChannel",
    channelTitle = "Example Channel Title",
    liveStatus = LiveVideoStatus.LIVE,
    author = "John Doe",
    publishDate = instantOf("2024-11-10T14:00:00Z"),
    publishEndDate = instantOf("2024-11-10T16:00:00Z"),
    startDateUTC = instantOf("2024-11-10T15:00:00Z"),
    canCutAndPublish = true,
    canStopAndCut = true,
)

val exampleLiveVideoResponseV2Minimal = LiveVideoResponseV2Minimal(
    id = "67890fghij",
    liveStatus = LiveVideoStatus.LIVE,
    startDateUTC = instantOf("2024-11-10T15:00:00Z"),
    viewerCount = 100,
)

val exampleGjirafaCutRequest = CutLivestreamRequest(
    title = "Sample Video",
    fromMilisecond = 0,
    toMilisecond = 10000,
)

val exampleGjirafaStopAndCutRequest = StopAndCutRequest(
    title = "Video name",
    fromMilisecond = 0,
    toMilisecond = 10000,
)

val exampleGjirafaCutResponse = CutLivestreamResponse(
    videoPublicId = "abcdef",
    name = "Stream name",
    originalFile = "https://original-file",
)

val exampleGjirafaVideoThumbnailResponse = PostLiveVideoThumbnail(
    id = "video-id",
    path = "https://thumbnail-url",
)

val examplePostLiveVideoRequest = PostLiveVideoRequest(
    title = "live-stream",
    author = "stream-author",
    description = "stream-description",
    /** to be left null, will be set up automatically according to creator */
    channelId = null,
    dvrEnabled = false,
    rewindDuration = 10,
    rewindTypeId = 10,
    publishDate = Instant.now(),
    publishEndDate = Instant.now(),
)
