package hero.api.messages.controller.dto

import hero.api.post.controller.dto.examplePostResponse
import hero.api.user.controller.dto.exampleUserResponse
import hero.model.SubscriptionRelationType
import java.time.Instant

val examplePaginatedMessageThreadResponse = PagedMessageThreadsResponse(
    content = listOf(
        MessageThreadResponse(
            id = "1683706670998-1431998399",
            participants = listOf(exampleUserResponse),
            participantIds = setOf("user1", "user2"),
            createdAt = Instant.now(),
            seenAt = Instant.now(),
            checkedAt = Instant.now(),
            lastMessageAt = Instant.now(),
            deleted = true,
            archived = false,
            canMessage = false,
            deletedAt = Instant.now(),
            lastMessage = examplePostResponse,
        ),
    ),
    hasNext = true,
    afterCursor = "eyJsYXN0TWVzc2FnZUF0IjoiMjAyMy0wOS0wNVQwODo0MzoxMC42MTY0MzFaIn0=",
    beforeCursor = null,
)

val exampleMessageThreadDetailsResponse = MessageThreadDetailsResponse(
    id = "1683706670998-1431998399",
    participants = listOf(exampleUserResponse),
    participantIds = listOf("user1", "user2"),
    createdAt = Instant.now(),
    seenAt = Instant.now(),
    checkedAt = Instant.now(),
    lastMessageAt = Instant.now(),
    deleted = true,
    archived = false,
    deletedAt = Instant.now(),
    canPost = true,
    commonCreators = listOf("user3"),
    relation = SubscriptionRelationType.IS_SUBSCRIBED_TO_SAME_CREATOR,
)

val exampleCreateMessageRequest = CreateMessageRequest(
    text = "text",
)
