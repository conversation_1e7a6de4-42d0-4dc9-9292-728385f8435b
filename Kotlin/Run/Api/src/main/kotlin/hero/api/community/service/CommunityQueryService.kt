package hero.api.community.service

import hero.exceptions.http.NotFoundException
import hero.model.Community
import hero.repository.community.CommunityRepository
import hero.repository.community.JooqCommunityHelper
import hero.sql.jooq.Tables.COMMUNITY
import org.jooq.DSLContext
import org.jooq.exception.NoDataFoundException
import java.util.UUID

class CommunityQueryService(
    lazyContext: Lazy<DSLContext>,
    private val communityRepository: CommunityRepository,
) {
    private val context by lazyContext

    fun execute(query: GetCommunity): Community {
        try {
            val communityId = UUID.fromString(query.communityId)
            return communityRepository.getById(communityId)
        } catch (_: IllegalArgumentException) {
            return context.select(JooqCommunityHelper.communityFields)
                .from(COMMUNITY)
                .where(COMMUNITY.SLUG.eq(query.communityId))
                .runCatching { JooqCommunityHelper.mapRecordToEntity(fetchSingle()) }
                .getOrElse {
                    when (it) {
                        is NoDataFoundException -> throw NotFoundException()
                        else -> throw it
                    }
                }
        }
    }
}

data class GetCommunity(val communityId: String)
