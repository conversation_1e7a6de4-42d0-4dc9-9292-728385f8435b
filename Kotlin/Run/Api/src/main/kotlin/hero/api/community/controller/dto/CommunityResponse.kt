package hero.api.community.controller.dto

import hero.api.user.controller.dto.UserResponse
import hero.model.ImageAsset
import java.time.Instant
import java.util.UUID

data class CommunityResponse(
    val id: UUID,
    val name: String,
    val description: String,
    val slug: String,
    val ownerId: String,
    val owner: UserResponse,
    val membersCount: Long,
    val image: ImageAsset?,
    val createdAt: Instant,
    val isVerified: <PERSON><PERSON><PERSON>,
)
