package hero.api.invoice.util

import com.github.kittinunf.fuel.core.extensions.authentication
import com.github.kittinunf.fuel.httpGet
import hero.baseutils.fetch
import hero.baseutils.retryOn
import hero.baseutils.systemEnv
import hero.exceptions.http.BadRequestException
import hero.exceptions.http.NotFoundException
import hero.model.Currency
import java.io.IOException
import java.math.BigDecimal
import java.time.LocalDate

private val flexibeeEurRatesCache: MutableMap<Pair<Currency, LocalDate>, BigDecimal> = mutableMapOf()

fun fetchCzkConversionRate(
    currency: Currency,
    date: LocalDate,
): BigDecimal =
    flexibeeEurRatesCache.getOrPut(Pair(currency, date)) {
        if (date > LocalDate.now()) {
            throw BadRequestException("Cannot request conversion rate for future: $date")
        }
        if (currency == Currency.CZK) {
            throw BadRequestException("Cannot request conversion rate for CZK.")
        }
        retryOn(IOException::class) {
            (
                "https://herohero.flexibee.eu/c/herohero/kurz/" +
                    "(mena='code:$currency' and platiOdData<='$date').json?limit=1&order=platiOdData@D"
            )
                .httpGet()
                .authentication().basic("herohero-api", systemEnv("FLEXIBEE_PASSWORD"))
                .fetch<FlexibeeKurzResponse>()
                .winstrom
                .kurz
                .firstOrNull()
                ?.nbStred
                ?: throw NotFoundException("Cannot find conversion rate for $currency on $date")
        }
    }

private data class FlexibeeKurzResponse(
    val winstrom: FlexibeeKurzWinstrom,
)

private data class FlexibeeKurzWinstrom(
    val kurz: List<FlexibeeKurz>,
)

private data class FlexibeeKurz(
    // cannot be LocalDate as it contains timezone, e.g.: 2025-08-18+02:00
    val platiOdData: String,
    // e.g.: code:EUR
    val mena: String,
    val nbStred: BigDecimal,
)
