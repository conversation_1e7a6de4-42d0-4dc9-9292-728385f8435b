package hero.api.messages.service

import com.google.firebase.messaging.AndroidConfig
import com.google.firebase.messaging.AndroidNotification
import com.google.firebase.messaging.ApnsConfig
import com.google.firebase.messaging.Aps
import com.google.firebase.messaging.ApsAlert
import com.google.firebase.messaging.FirebaseMessaging
import com.google.firebase.messaging.MulticastMessage
import com.google.firebase.messaging.Notification
import hero.api.post.service.CreatePost
import hero.api.post.service.PostService
import hero.baseutils.log
import hero.exceptions.http.ForbiddenException
import hero.gcloud.TypedCollectionReference
import hero.messaging.sendMulticastMessage
import hero.model.MessageThread
import hero.model.Post
import hero.repository.device.fetchFirebaseRegistrationTokens
import hero.repository.user.UserRepository
import org.jooq.DSLContext
import java.time.Clock
import java.time.Instant
import kotlin.concurrent.thread

class MessageCommandService(
    // todo replace with repository
    private val messageThreadsCollection: TypedCollectionReference<MessageThread>,
    private val postService: PostService,
    private val userRepository: UserRepository,
    private val firebaseMessaging: FirebaseMessaging,
    lazyContext: Lazy<DSLContext>,
    private val clock: Clock = Clock.systemUTC(),
) {
    private val context by lazyContext

    fun execute(command: SendMessage): Post {
        val messageThread = messageThreadsCollection[command.messageThreadId].get()

        if (command.userId !in messageThread.userIds) {
            throw ForbiddenException(
                "User ${command.userId} is not part of the message thread ${command.messageThreadId}",
            )
        }

        if (!messageThread.canMessage.getValue(command.userId)) {
            throw ForbiddenException(
                "User ${command.userId} cannot send message to the message thread ${command.messageThreadId}",
            )
        }

        val message = postService.execute(
            CreatePost(
                userId = command.userId,
                messageThreadId = command.messageThreadId,
                publishedAt = Instant.now(clock),
                text = command.text,
                textHtml = null,
                textDelta = null,
                assets = listOf(),
                isSponsored = false,
                isAgeRestricted = false,
            ),
        )

        // TODO there is an issue with data race here, we can fix this with locking only
        // a message can come, which will take a while to be processed for unknown reasons, second message comes
        // updates the message thread, but then first message is processed and updates the message thread to old data
        messageThreadsCollection[messageThread.id].set(
            messageThread.copy(
                lastMessageAt = message.published,
                lastMessageBy = message.userId,
                lastMessageId = message.id,
                seens = messageThread.seens.plus(command.userId to message.published),
                checks = messageThread.checks.plus(command.userId to message.published),
            ),
        )

        val sender = userRepository.getById(command.userId)
        val otherUserIds = messageThread.userIds - command.userId
        otherUserIds.forEach { userId ->
            // refactor this so we don't have to repeat on every notification
            thread {
                val tokens = fetchFirebaseRegistrationTokens(context, userId)
                if (tokens.isNotEmpty()) {
                    val key = "push_notification_new_message_body"
                    val apsAlert = ApsAlert.builder().setLocalizationKey(key).addLocalizationArg(message.text).build()
                    val aps = Aps.builder().setAlert(apsAlert).build()
                    val apnsConfig = ApnsConfig.builder().setAps(aps).build()

                    val androidConfig = AndroidConfig.builder()
                        .setNotification(
                            AndroidNotification.builder()
                                .setBodyLocalizationKey(key)
                                .addBodyLocalizationArg(message.text)
                                .build(),
                        )
                        .build()

                    val message = MulticastMessage.builder()
                        .putData("notification_type", "NEW_MESSAGE")
                        .putData("message_thread_id", messageThread.id)
                        .putData("message_id", message.id)
                        .putData("sender_id", sender.id)
                        .setNotification(Notification.builder().setTitle(sender.name).build())
                        .setAndroidConfig(androidConfig)
                        .setApnsConfig(apnsConfig)

                    sendMulticastMessage(firebaseMessaging, message, tokens, userId, log)
                }
            }
        }

        return message
    }
}

data class SendMessage(
    val userId: String,
    val messageThreadId: String,
    val text: String,
)
