package hero.api.payment.service

import com.stripe.model.Coupon
import com.stripe.param.CouponCreateParams
import hero.api.invoice.util.fetchCzkConversionRate
import hero.api.payment.controller.PaymentResponse
import hero.api.subscriber.repository.SubscriberStripeRepository
import hero.api.user.repository.UsersRepository
import hero.baseutils.log
import hero.exceptions.http.ConflictException
import hero.gcloud.TypedCollectionReference
import hero.model.AppleCharge
import hero.model.CouponMethod
import hero.model.Currency
import hero.model.Subscriber
import hero.model.Tier
import hero.model.User
import hero.model.topics.CardCreateType
import hero.stripe.service.StripeClients
import java.time.Instant
import java.time.ZoneOffset
import java.util.UUID

class StripeAppleMirroringService(
    private val subscriberStripeRepository: SubscriberStripeRepository,
    private val clients: StripeClients,
    private val userRepository: UsersRepository,
    private val transfersCollection: TypedCollectionReference<AppleCharge>,
) {
    private fun appleCouponFactory(
        userId: String,
        creator: User,
        tier: Tier,
    ): Coupon {
        val price = subscriberStripeRepository.priceFactory(creator, tier)
        return clients[tier.currency].coupons().create(
            CouponCreateParams.builder()
                .setId("APPLE-${tier.currency}-${UUID.randomUUID()}")
                .setDuration(CouponCreateParams.Duration.FOREVER)
                .setAppliesTo(
                    CouponCreateParams.AppliesTo.builder()
                        .addProduct(price.product)
                        .build(),
                )
                .setPercentOff(100.toBigDecimal())
                .setMetadata(
                    mapOf(
                        "purchasedByUserId" to userId,
                        "couponMethod" to CouponMethod.APPLE_IN_APP.name,
                        "creatorId" to creator.id,
                    ),
                )
                .build(),
        )
    }

    fun createSubscription(
        appleReferenceId: String,
        appleTransactionId: String,
        userId: String,
        creatorId: String,
        tierId: String,
        currencyStore: Currency,
        priceStoreCents: Long,
    ): PaymentResponse {
        val tier = Tier.ofId(tierId)
        val user = userRepository.get(userId)
        val creator = userRepository.get(creatorId)

        if (!creator.creator.active) {
            throw ConflictException(
                "Creator ${creator.id} has not finished their Stripe account pairing.",
                mapOf("creatorId" to creator.id, "appleReferenceId" to appleReferenceId),
            )
        }

        if (creator.id == user.id) {
            throw ConflictException(
                "User ${user.id} cannot subscribe themselves.",
                mapOf("userId" to user.id, "appleReferenceId" to appleReferenceId),
            )
        }

        val coupon = appleCouponFactory(userId, creator, tier)
        val subscription = subscriberStripeRepository.subscribe(
            user = user,
            creator = creator,
            paymentMethodId = null,
            couponId = coupon.id,
            cardCreateType = CardCreateType.APPLE_IN_APP,
            metadata = mapOf(
                "Apple Subscription \uF8FF" to "Do not cancel ❌",
                "\uF8FF Apple Subscription" to "Do not cancel ❌",
                Subscriber::appleReferenceId.name to appleReferenceId,
                Subscriber::appleTransactionId.name to appleTransactionId,
            ),
        )

        // transfer must also be made as this is an initial payment
        storeCharge(
            appleReferenceId,
            appleTransactionId,
            Instant.now(),
            tierId,
            creatorId,
            userId,
            currencyStore,
            priceStoreCents,
        )

        log.info(
            "Replicated Apple subscription $appleReferenceId of ${user.id} -> ${creator.id}" +
                " as ${subscription.attributes.stripeId}/${subscription.attributes.subscriptionStatus}",
            mapOf("userId" to user.id, "creatorId" to creator.id, "appleReferenceId" to appleReferenceId),
        )
        return subscription
    }

    fun appleCancelledAtPeriodEnd(
        appleReferenceId: String,
        cancelAtPeriodEnd: Boolean,
    ) {
        subscriberStripeRepository.patchSubscription(appleReferenceId, cancelAtPeriodEnd)
    }

    fun appleCancelledNow(appleReferenceId: String) {
        subscriberStripeRepository.cancelSubscription(appleReferenceId)
    }

    fun assertNotExisting(
        userId: String,
        creatorId: String,
        currency: Currency,
    ) {
        val sub = subscriberStripeRepository.getActiveStripeSubscription(
            userRepository.get(userId),
            userRepository.get(creatorId),
            currency,
        )
        if (sub != null) {
            throw ConflictException(
                "Subscription for $userId -> $creatorId already exists",
                mapOf("userId" to userId, "creatorId" to creatorId),
            )
        }
    }

    fun storeCharge(
        appleReferenceId: String,
        appleTransactionId: String,
        timestamp: Instant,
        tierId: String,
        creatorId: String,
        userId: String,
        currencyStore: Currency,
        priceStoreCents: Long,
    ) {
        val creator = userRepository.get(creatorId)

        // as function below goes through accounting software of Flexibee, we can fetch only EUR->CZK rates
        // if we ever support currencies like SEK/DAK, we have to extend the conversion rates to different provider
        if (currencyStore !in setOf(Currency.EUR, Currency.CZK)) {
            error("Unsupported currency $currencyStore for $appleReferenceId/$appleTransactionId")
        }

        val tier = Tier.ofId(tierId)

        val conversionRateCents = if (currencyStore == Currency.CZK)
            fetchCzkConversionRate(tier.currency, timestamp.atZone(ZoneOffset.UTC).toLocalDate())
                .movePointRight(2).toLong()
        else
            1_00L

        val feeHerohero = priceStoreCents - (tier.priceCents * conversionRateCents / 1_00)

        storeCharge(
            AppleCharge(
                appleTransactionId = appleTransactionId,
                appleReferenceId = appleReferenceId,
                createdAt = Instant.now(),
                userId = userId,
                creatorId = creatorId,
                tierId = tierId,
                targetAccountId = creator.creator.stripeAccountId!!,
                stripeTransferId = null,
                transferredAt = null,
                currencyStore = currencyStore,
                priceStoreCents = priceStoreCents,
                conversionRateCents = conversionRateCents,
                priceFeeHeroheroCents = feeHerohero,
            ),
        )
    }

    internal fun storeCharge(charge: AppleCharge) {
        transfersCollection[charge.appleTransactionId].set(charge)
    }
}
