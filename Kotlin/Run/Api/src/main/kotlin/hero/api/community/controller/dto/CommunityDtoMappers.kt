package hero.api.community.controller.dto

import hero.api.user.controller.dto.toResponse
import hero.model.Community
import hero.model.User

fun Community.toResponse(user: User) =
    CommunityResponse(
        id = id,
        name = name,
        description = description,
        slug = slug,
        ownerId = ownerId,
        membersCount = membersCount,
        image = image,
        createdAt = createdAt,
        isVerified = user.creator.verified,
        owner = user.toResponse(listOf()),
    )
