package hero.api.invoice.util

import hero.exceptions.http.BadRequestException
import hero.model.Currency
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.time.LocalDate
import kotlin.test.assertEquals
import kotlin.test.assertNotEquals
import kotlin.test.assertTrue

class ConversionRatesUtilsTest {
    @Test
    fun `should fetch conversion rate for today`() {
        val rateEur = fetchCzkConversionRate(Currency.EUR, LocalDate.now())
        val rateEurFixed = fetchCzkConversionRate(Currency.EUR, LocalDate.of(2025, 1, 1))
        val rateUsd = fetchCzkConversionRate(Currency.USD, LocalDate.now())
        val rateUsdFixed = fetchCzkConversionRate(Currency.USD, LocalDate.of(2025, 1, 1))
        assertTrue { rateEur > "20".toBigDecimal() }
        assertTrue { rateUsd > "20".toBigDecimal() }
        assertEquals("25.185".toBigDecimal(), rateEurFixed)
        assertEquals("24.237".toBigDecimal(), rateUsdFixed)
        assertNotEquals(rateEur, rateUsd)
    }

    @Test
    fun `should fail to fetch conversion rate for future`() {
        assertThrows<BadRequestException> {
            fetchCzkConversionRate(Currency.EUR, LocalDate.now().plusDays(2))
        }
        assertThrows<BadRequestException> {
            fetchCzkConversionRate(Currency.USD, LocalDate.now().plusDays(2))
        }
    }
}
