package hero.api.community.service

import hero.exceptions.http.ConflictException
import hero.exceptions.http.ForbiddenException
import hero.model.topics.CommunityCreated
import hero.repository.community.CommunityRepository
import hero.sql.jooq.Tables.USER
import hero.test.IntegrationTest
import hero.test.TestRepositories
import hero.test.time.TestClock
import io.mockk.slot
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.junit.jupiter.api.Test
import java.time.Instant

class CommunityCommandServiceIT : IntegrationTest() {
    @Test
    fun `should create a community for a user who can create communities`() {
        val now = Instant.ofEpochSecond(1755450161)
        val testClock = TestClock(now)
        val communityRepository = CommunityRepository(testContext)
        val underTest = CommunityCommandService(
            communityRepository = communityRepository,
            userRepository = TestRepositories.userRepository,
            pubSub = pubSubMock,
            lazyContext = lazyTestContext,
            clock = testClock,
        )

        val user = testHelper.createUser("filip", canCreateCommunity = true)

        val community = underTest.execute(CreateCommunity("filip"))

        with(communityRepository.getById(community.id)) {
            assertThat(this).isEqualTo(community)
            assertThat(name).isEqualTo(user.name)
            assertThat(description).isEqualTo("Community")
            assertThat(slug).isEqualTo(user.path)
            assertThat(ownerId).isEqualTo("filip")
            assertThat(membersCount).isEqualTo(1)
            assertThat(image).isEqualTo(user.image)
            assertThat(createdAt).isEqualTo(now)
            assertThat(updatedAt).isEqualTo(now)
            assertThat(deletedAt).isNull()
        }

        val publishedEvent = slot<CommunityCreated>()
        verify { pubSubMock.publish(capture(publishedEvent)) }
        assertThat(publishedEvent.captured.communityId).isEqualTo(community.id)

        assertThat(testContext.selectFrom(USER).where(USER.ID.eq("filip")).fetchSingle().ownedCommunitiesCount)
            .isEqualTo(1)
    }

    @Test
    fun `user cannot create a community if they are not allowed to create communities`() {
        val communityRepository = CommunityRepository(testContext)
        val underTest = CommunityCommandService(
            communityRepository = communityRepository,
            userRepository = TestRepositories.userRepository,
            lazyContext = lazyTestContext,
            pubSub = pubSubMock,
        )

        testHelper.createUser("filip", canCreateCommunity = false)

        assertThatExceptionOfType(ForbiddenException::class.java).isThrownBy {
            underTest.execute(CreateCommunity("filip"))
        }.withMessage("User filip cannot create communities")
    }

    @Test
    fun `user cannot create a community if they already have one`() {
        val communityRepository = CommunityRepository(testContext)
        val underTest = CommunityCommandService(
            communityRepository = communityRepository,
            userRepository = TestRepositories.userRepository,
            lazyContext = lazyTestContext,
            pubSub = pubSubMock,
        )

        testHelper.createUser("filip", canCreateCommunity = true)

        // Create first community
        underTest.execute(CreateCommunity("filip"))

        // Try to create second community
        assertThatExceptionOfType(ConflictException::class.java).isThrownBy {
            underTest.execute(CreateCommunity("filip"))
        }.withMessage("User filip already has a community")
    }
}
