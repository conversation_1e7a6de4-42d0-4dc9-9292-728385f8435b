package hero.api.community.service

import hero.exceptions.http.NotFoundException
import hero.repository.community.CommunityRepository
import hero.test.IntegrationTest
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.junit.jupiter.api.Test
import java.util.UUID

class CommunityQueryServiceIT : IntegrationTest() {
    @Test
    fun `should get community by id`() {
        val communityRepository = CommunityRepository(testContext)
        val underTest = CommunityQueryService(
            lazyContext = lazyTestContext,
            communityRepository = communityRepository,
        )

        testHelper.createUser("owner-id", canCreateCommunity = true)
        val community = testHelper.createCommunity(
            ownerId = "owner-id",
            name = "Test Community",
            description = "Test Description",
            slug = "test-community",
        )

        val result = underTest.execute(GetCommunity(community.id.toString()))

        assertThat(result).isEqualTo(community)
        assertThat(result.name).isEqualTo("Test Community")
        assertThat(result.description).isEqualTo("Test Description")
        assertThat(result.slug).isEqualTo("test-community")
        assertThat(result.ownerId).isEqualTo("owner-id")
    }

    @Test
    fun `should get community by slug`() {
        val communityRepository = CommunityRepository(testContext)
        val underTest = CommunityQueryService(
            lazyContext = lazyTestContext,
            communityRepository = communityRepository,
        )

        testHelper.createUser("owner-id", canCreateCommunity = true)
        val community = testHelper.createCommunity(
            ownerId = "owner-id",
            name = "Test Community",
            description = "Test Description",
            slug = "test-community-slug",
        )

        val result = underTest.execute(GetCommunity("test-community-slug"))

        assertThat(result).isEqualTo(community)
        assertThat(result.name).isEqualTo("Test Community")
        assertThat(result.description).isEqualTo("Test Description")
        assertThat(result.slug).isEqualTo("test-community-slug")
        assertThat(result.ownerId).isEqualTo("owner-id")
    }

    @Test
    fun `should throw NotFoundException when community does not exist by id`() {
        val communityRepository = CommunityRepository(testContext)
        val underTest = CommunityQueryService(
            lazyContext = lazyTestContext,
            communityRepository = communityRepository,
        )

        val nonExistentId = UUID.randomUUID()

        assertThatExceptionOfType(NotFoundException::class.java).isThrownBy {
            underTest.execute(GetCommunity(nonExistentId.toString()))
        }
    }

    @Test
    fun `should throw NotFoundException when community does not exist by slug`() {
        val communityRepository = CommunityRepository(testContext)
        val underTest = CommunityQueryService(
            lazyContext = lazyTestContext,
            communityRepository = communityRepository,
        )

        assertThatExceptionOfType(NotFoundException::class.java).isThrownBy {
            underTest.execute(GetCommunity("non-existent-slug"))
        }
    }
}
