/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { VideoAssetsModel } from './VideoAssetsModel';

export type VideoAssetsModelResponseModel = {
    readonly success?: boolean;
    errors?: Array<string> | null;
    messages?: Array<string> | null;
    result?: VideoAssetsModel;
    resultInfo?: string | null;
    statusCode?: number;
};

