/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { Currency } from './Currency';
import type { InvoiceItemDto } from './InvoiceItemDto';
import type { UserCompanyPublic } from './UserCompanyPublic';
export type InvoiceDtoAttributes = {
    stripeAccountId?: string | null;
    stripePayoutId?: string | null;
    timestamp: string;
    total: number;
    total4D?: number | null;
    currency: Currency;
    currencyPayout: Currency;
    issuingCompany: UserCompanyPublic;
    invoicedCompany: UserCompanyPublic;
    euReverseCharged: boolean;
    eurConversionRateCents?: number | null;
    authToken: string;
    items: Array<InvoiceItemDto>;
    totalFeesAndChargesVatExcluded: number;
    totalFeesAndCharges: number;
    totalFeesAndChargesVat: number;
    totalFeesAndChargesVatExcluded4D?: number | null;
    totalFeesAndCharges4D?: number | null;
    totalFeesAndChargesVat4D?: number | null;
};

