/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { InvoiceDtoAttributes } from './InvoiceDtoAttributes';
import type { InvoiceDtoRelationships } from './InvoiceDtoRelationships';
export type InvoiceDto = {
    id: string;
    attributes: InvoiceDtoAttributes;
    relationships: InvoiceDtoRelationships;
    type: string;
};

