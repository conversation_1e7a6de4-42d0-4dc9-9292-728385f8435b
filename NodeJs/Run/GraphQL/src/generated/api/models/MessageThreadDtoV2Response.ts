/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { ListResponseMeta } from './ListResponseMeta';
import type { MessageThreadDto } from './MessageThreadDto';
import type { MessageThreadResponseIncluded } from './MessageThreadResponseIncluded';
export type MessageThreadDtoV2Response = {
    meta: ListResponseMeta;
    messageThreads: Array<MessageThreadDto>;
    included: MessageThreadResponseIncluded;
};

