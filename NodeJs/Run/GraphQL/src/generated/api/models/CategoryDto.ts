/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { CategoryDtoAttributes } from './CategoryDtoAttributes';
import type { CategoryDtoRelationships } from './CategoryDtoRelationships';
export type CategoryDto = {
    id?: string | null;
    attributes: CategoryDtoAttributes;
    relationships: CategoryDtoRelationships;
    type: string;
};

