/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
export enum StripeDeclineCode {
    AUTHENTICATION_REQUIRED = 'authentication_required',
    APPROVE_WITH_ID = 'approve_with_id',
    CALL_ISSUER = 'call_issuer',
    CARD_NOT_SUPPORTED = 'card_not_supported',
    CARD_VELOCITY_EXCEEDED = 'card_velocity_exceeded',
    CURRENCY_NOT_SUPPORTED = 'currency_not_supported',
    DO_NOT_HONOR = 'do_not_honor',
    DO_NOT_TRY_AGAIN = 'do_not_try_again',
    DUPLICATE_TRANSACTION = 'duplicate_transaction',
    EXPIRED_CARD = 'expired_card',
    FRAUDULENT = 'fraudulent',
    GENERIC_DECLINE = 'generic_decline',
    INCORRECT_CVC = 'incorrect_cvc',
    INCORRECT_NUMBER = 'incorrect_number',
    INCORRECT_PIN = 'incorrect_pin',
    INSUFFICIENT_FUNDS = 'insufficient_funds',
    INVALID_ACCOUNT = 'invalid_account',
    INVALID_AMOUNT = 'invalid_amount',
    INVALID_CVC = 'invalid_cvc',
    INVALID_EXPIRY_DATE = 'invalid_expiry_date',
    INVALID_NUMBER = 'invalid_number',
    ISSUER_NOT_AVAILABLE = 'issuer_not_available',
    LOST_CARD = 'lost_card',
    MERCHANT_BLACKLIST = 'merchant_blacklist',
    NEW_ACCOUNT_INFORMATION_AVAILABLE = 'new_account_information_available',
    NO_ACTION_TAKEN = 'no_action_taken',
    NOT_PERMITTED = 'not_permitted',
    PICKUP_CARD = 'pickup_card',
    PIN_TRY_EXCEEDED = 'pin_try_exceeded',
    PROCESSING_ERROR = 'processing_error',
    REENTER_TRANSACTION = 'reenter_transaction',
    RESTRICTED_CARD = 'restricted_card',
    REVOCATION_OF_ALL_AUTHORIZATIONS = 'revocation_of_all_authorizations',
    REVOCATION_OF_AUTHORIZATION = 'revocation_of_authorization',
    SECURITY_VIOLATION = 'security_violation',
    SERVICE_NOT_ALLOWED = 'service_not_allowed',
    STOLEN_CARD = 'stolen_card',
    STOP_PAYMENT_ORDER = 'stop_payment_order',
    TESTMODE_DECLINE = 'testmode_decline',
    TRANSACTION_NOT_ALLOWED = 'transaction_not_allowed',
    TRY_AGAIN_LATER = 'try_again_later',
    WITHDRAWAL_COUNT_LIMIT_EXCEEDED = 'withdrawal_count_limit_exceeded',
}
