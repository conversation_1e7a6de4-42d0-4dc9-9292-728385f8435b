/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { SetupIntentStatus } from './SetupIntentStatus';
import type { StripeDeclineCode } from './StripeDeclineCode';
import type { StripeErrorCode } from './StripeErrorCode';
export type SetupResponse = {
    paymentMethodId: string;
    customerId: string;
    status: SetupIntentStatus;
    declineCode?: StripeDeclineCode;
    declineError?: StripeErrorCode;
};

