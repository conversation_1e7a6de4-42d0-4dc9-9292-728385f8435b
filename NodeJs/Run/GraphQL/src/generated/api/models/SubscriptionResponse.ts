/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { SubscriptionDetailsResponse } from './SubscriptionDetailsResponse';
import type { TierResponse } from './TierResponse';
import type { UserResponse } from './UserResponse';
export type SubscriptionResponse = {
    id: string;
    subscribedAt: string;
    details?: SubscriptionDetailsResponse;
    subscriber: UserResponse;
    creator: UserResponse;
    tier?: TierResponse;
};

