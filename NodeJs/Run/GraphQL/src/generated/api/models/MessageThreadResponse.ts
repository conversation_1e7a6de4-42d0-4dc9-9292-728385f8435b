/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { PostResponse } from './PostResponse';
import type { UserResponse } from './UserResponse';
export type MessageThreadResponse = {
    id: string;
    participants: Array<UserResponse>;
    participantIds: Array<string>;
    createdAt?: string | null;
    seenAt?: string | null;
    checkedAt?: string | null;
    lastMessageAt?: string | null;
    canMessage: boolean;
    deleted: boolean;
    archived: boolean;
    deletedAt?: string | null;
    lastMessage: PostResponse;
};

