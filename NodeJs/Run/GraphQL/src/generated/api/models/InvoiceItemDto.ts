/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { InvoiceItemType } from './InvoiceItemType';
export type InvoiceItemDto = {
    title?: string | null;
    priceUnit: number;
    priceUnit4D?: number | null;
    count: number;
    count4D?: number | null;
    priceTotal: number;
    priceTotal4D?: number | null;
    type: InvoiceItemType;
    vatRate: number;
    metadata: Record<string, any>;
    priceUnitVatExcluded: number;
    priceUnitVatExcluded4D?: number | null;
    priceTotalVatExcluded: number;
    priceTotalVatExcluded4D?: number | null;
    totalVat: number;
    totalVat4D?: number | null;
};

