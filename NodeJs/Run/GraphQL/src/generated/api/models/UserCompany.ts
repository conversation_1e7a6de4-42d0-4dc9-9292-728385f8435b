/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { CompanyType } from './CompanyType';
import type { VatPayer } from './VatPayer';
export type UserCompany = {
    namePublic?: string | null;
    isIndividual?: boolean | null;
    companyType?: CompanyType;
    name?: string | null;
    firstName?: string | null;
    lastName?: string | null;
    address?: string | null;
    postalCode?: string | null;
    country?: string | null;
    birthDate?: string | null;
    city?: string | null;
    state?: string | null;
    phone?: string | null;
    id?: string | null;
    vatId?: string | null;
    additionalInfo?: string | null;
    registeredWith?: string | null;
    vatType: VatPayer;
    iban?: string | null;
    swift?: string | null;
};

