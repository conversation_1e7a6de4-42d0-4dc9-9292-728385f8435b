/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { PostDtoAttributes } from './PostDtoAttributes';
import type { PostDtoRelationships } from './PostDtoRelationships';
export type PostDto = {
    id?: string | null;
    attributes: PostDtoAttributes;
    relationships: PostDtoRelationships;
    type: string;
};

