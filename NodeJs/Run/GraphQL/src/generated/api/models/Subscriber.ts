/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { CancelledByRole } from './CancelledByRole';
import type { CouponMethod } from './CouponMethod';
import type { SubscriberStatus } from './SubscriberStatus';
import type { SubscriberType } from './SubscriberType';
export type Subscriber = {
    userId: string;
    creatorId: string;
    tierId: string;
    status: SubscriberStatus;
    cancelAtPeriodEnd: boolean;
    subscribed: string;
    expires?: string | null;
    cancelledAt?: string | null;
    cancelledBy?: string | null;
    cancelledByRole?: CancelledByRole;
    cancelledReason?: string | null;
    refused: boolean;
    subscriberType: SubscriberType;
    couponId?: string | null;
    couponPercentOff?: number | null;
    couponMethod?: CouponMethod;
    refunded: boolean;
    id: string;
};

