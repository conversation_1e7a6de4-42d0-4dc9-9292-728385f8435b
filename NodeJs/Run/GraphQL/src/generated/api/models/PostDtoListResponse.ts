/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { ListResponseMeta } from './ListResponseMeta';
import type { PostDto } from './PostDto';
import type { PostDtoListIncluded } from './PostDtoListIncluded';
export type PostDtoListResponse = {
    meta: ListResponseMeta;
    posts: Array<PostDto>;
    included?: PostDtoListIncluded;
};

