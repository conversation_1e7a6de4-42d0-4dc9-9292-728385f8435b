/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { SubscriptionRelationType } from './SubscriptionRelationType';
export type MessageThreadDtoAttributes = {
    createdAt?: string | null;
    canPost?: boolean | null;
    relation?: SubscriptionRelationType;
    commonCreators: Array<any>;
    seenAt?: string | null;
    checkedAt?: string | null;
    lastMessageAt?: string | null;
    deleted: boolean;
    archived: boolean;
};

