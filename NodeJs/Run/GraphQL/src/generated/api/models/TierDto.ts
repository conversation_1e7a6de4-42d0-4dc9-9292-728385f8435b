/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { TierDtoAttributes } from './TierDtoAttributes';
import type { TierDtoRelationships } from './TierDtoRelationships';
export type TierDto = {
    id?: string | null;
    attributes: TierDtoAttributes;
    relationships: TierDtoRelationships;
    type: string;
};

