/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { SubscriptionDtoAttributes } from './SubscriptionDtoAttributes';
import type { SubscriptionDtoRelationships } from './SubscriptionDtoRelationships';
export type SubscriptionDto = {
    id: string;
    attributes: SubscriptionDtoAttributes;
    relationships: SubscriptionDtoRelationships;
    type: string;
};

