/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { InvoiceDto } from './InvoiceDto';
import type { InvoicesDtoIncluded } from './InvoicesDtoIncluded';
import type { InvoicesDtoMeta } from './InvoicesDtoMeta';
export type InvoicesDtoResponse = {
    meta?: InvoicesDtoMeta;
    invoices: Array<InvoiceDto>;
    included: InvoicesDtoIncluded;
};

