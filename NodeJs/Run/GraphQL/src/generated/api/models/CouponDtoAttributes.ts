/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { CouponMethod } from './CouponMethod';
import type { CouponProvider } from './CouponProvider';
import type { CouponTarget } from './CouponTarget';
export type CouponDtoAttributes = {
    months?: number | null;
    percentOff?: number | null;
    redemptions: number;
    redeemBy?: string | null;
    target: CouponTarget;
    provider: CouponProvider;
    method: CouponMethod;
    campaign?: string | null;
};

