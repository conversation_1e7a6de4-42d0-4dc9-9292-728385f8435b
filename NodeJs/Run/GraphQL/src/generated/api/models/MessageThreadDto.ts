/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { MessageThreadDtoAttributes } from './MessageThreadDtoAttributes';
import type { MessageThreadDtoRelationships } from './MessageThreadDtoRelationships';
export type MessageThreadDto = {
    id?: string | null;
    attributes: MessageThreadDtoAttributes;
    relationships: MessageThreadDtoRelationships;
    type: string;
};

