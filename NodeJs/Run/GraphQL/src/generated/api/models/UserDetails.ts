/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { Creator } from './Creator';
import type { DiscordDtoAttributes } from './DiscordDtoAttributes';
import type { NotificationsEnabled } from './NotificationsEnabled';
export type UserDetails = {
    email?: string | null;
    creator?: Creator;
    discord?: DiscordDtoAttributes;
    notificationsEnabled?: NotificationsEnabled;
    language: string;
};

