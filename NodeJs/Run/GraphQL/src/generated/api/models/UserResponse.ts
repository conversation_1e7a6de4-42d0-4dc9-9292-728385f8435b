/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { Analytics } from './Analytics';
import type { CategoryResponse } from './CategoryResponse';
import type { ImageAsset } from './ImageAsset';
import type { SpotifyResponse } from './SpotifyResponse';
import type { TierResponse } from './TierResponse';
import type { UserCountsResponse } from './UserCountsResponse';
import type { UserProfileType } from './UserProfileType';
export type UserResponse = {
    id: string;
    name: string;
    bio: string;
    bioHtml: string;
    bioEn: string;
    bioHtmlEn: string;
    path: string;
    image?: ImageAsset;
    hasRssFeed: boolean;
    counts: UserCountsResponse;
    verified: boolean;
    subscribable: boolean;
    tier: TierResponse;
    categories: Array<CategoryResponse>;
    analytics: Analytics;
    spotify?: SpotifyResponse;
    privacyPolicyEnabled: boolean;
    emailPublic?: string | null;
    hasGiftsAllowed: boolean;
    isDeleted: boolean;
    profileType: UserProfileType;
};

