/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { DocumentAsset } from './DocumentAsset';
import type { GjirafaAsset } from './GjirafaAsset';
import type { GjirafaLiveAsset } from './GjirafaLiveAsset';
import type { ImageAssetDto } from './ImageAssetDto';
import type { YouTubeAsset } from './YouTubeAsset';
export type PostAssetDto = {
    image?: ImageAssetDto;
    youTube?: YouTubeAsset;
    gjirafa?: GjirafaAsset;
    gjirafaLive?: GjirafaLiveAsset;
    document?: DocumentAsset;
    thumbnail?: string | null;
    bunnyAsset?: string | null;
    audioAsset?: string | null;
    thumbnailImage?: ImageAssetDto;
};

