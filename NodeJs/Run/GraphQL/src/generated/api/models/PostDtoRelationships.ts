/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { CategoryDtoRelationship } from './CategoryDtoRelationship';
import type { MessageThreadDtoV2Relationship } from './MessageThreadDtoV2Relationship';
import type { PostDtoRelationship } from './PostDtoRelationship';
import type { UserDtoRelationship } from './UserDtoRelationship';
export type PostDtoRelationships = {
    parent?: PostDtoRelationship;
    sibling?: PostDtoRelationship;
    user?: UserDtoRelationship;
    messageThread?: MessageThreadDtoV2Relationship;
    paymentsByUsers: Array<UserDtoRelationship>;
    categories: Array<CategoryDtoRelationship>;
};

