/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { PaymentResponseAttributes } from './PaymentResponseAttributes';
import type { PaymentResponseRelationships } from './PaymentResponseRelationships';
export type PaymentResponse = {
    attributes: PaymentResponseAttributes;
    relationships: PaymentResponseRelationships;
};

