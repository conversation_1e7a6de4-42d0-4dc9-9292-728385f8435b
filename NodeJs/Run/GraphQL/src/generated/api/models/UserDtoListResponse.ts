/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { ListResponseMeta } from './ListResponseMeta';
import type { UserDto } from './UserDto';
import type { UserDtoIncluded } from './UserDtoIncluded';
export type UserDtoListResponse = {
    meta: ListResponseMeta;
    users: Array<UserDto>;
    included: UserDtoIncluded;
};

