import { SubscriptionRelationType } from '../generated/resolvers-types'
import {
    PostAssetModel,
    PostBunnyAsset,
    PostDocumentAsset,
    PostEmptyAsset,
    PostGjirafaAsset,
    PostGjirafaLivestreamAsset,
    PostImageAsset,
    PostYoutubeAsset,
} from './post'
import { UserModel } from './user'

export type MessageThreadModel = {
    id: string
    participants: UserModel[]
    participantIds: string[]
    canMessage: boolean
    createdAt?: string | null
    seenAt?: string | null
    checkedAt?: string | null
    lastMessageAt?: string | null
    lastMessage?: MessageModel | null
}

export type MessageThreadDetailsModel = {
    id: string
    participants: UserModel[]
    participantIds: string[]
    createdAt?: string | null
    canPost: boolean
    relation: SubscriptionRelationType
    commonCreators: string[]
    seenAt?: string | null
    checkedAt?: string | null
    lastMessageAt?: string | null
    deletedAt?: string | null
    deleted: boolean
    archived: boolean
    lastMessage?: MessageModel | null
}

export type MessageModel = {
    id: string
    text?: string | null
    textHtml?: string | null
    sentAt?: string | null
    sentById: string
    assets: PostAssetModel[]
    fullAssets?: boolean | null
    price?: number | null
    messageThreadId: string
}

export type MessageAssetModel =
    | PostDocumentAsset
    | PostImageAsset
    | PostGjirafaAsset
    | PostGjirafaLivestreamAsset
    | PostYoutubeAsset
    | PostBunnyAsset
    | PostEmptyAsset
