import { ServiceDataSource } from './ServiceDataSource'
import { Environment } from '../common/environment'
import { DataSourceConfig } from '@apollo/datasource-rest'
import { CommunityModel } from '../models/community'
import { CommunityResponse } from '../generated/api'
import { mapToUser } from './common-mappers'

export class CommunityAPI extends ServiceDataSource {
    constructor(environment: Environment, cookies?: string, config?: DataSourceConfig) {
        super(environment, 'api', cookies, config)
    }

    async getCommunity(communityId: string): Promise<CommunityModel> {
        const response = await this.get<CommunityResponse>(`/v1/communities/${communityId}`)

        return {
            id: response.id,
            name: response.name,
            description: response.description,
            slug: response.slug,
            ownerId: response.ownerId,
            owner: mapToUser(response.owner),
            membersCount: response.membersCount,
            ...(response.image && {
                image: {
                    url: response.image.id,
                    height: response.image.height,
                    width: response.image.width,
                    hidden: response.image.hidden,
                },
            }),
            createdAt: response.createdAt,
            isVerified: response.isVerified,
        }
    }
}
