import type { CodegenConfig } from '@graphql-codegen/cli'
import { DateTimeResolver } from 'graphql-scalars'

const config: CodegenConfig = {
    overwrite: true,
    schema: 'schema.graphql',
    generates: {
        'src/generated/resolvers-types.ts': {
            plugins: ['typescript', 'typescript-resolvers'],
            config: {
                inputMaybeValue: 'T | undefined | null',
                contextType: '../context#DataSourceContext',
                makeResolverTypeCallable: true,
                mappers: {
                    Community: '../models/community#CommunityModel',
                    Notification: '../models/notification#NotificationModel',
                    PostNotification: '../models/notification#NotificationModel',
                    SubscriberNotification: '../models/notification#NotificationModel',
                    SubscriptionNotification: '../models/notification#NotificationModel',
                    RequestNotification: '../models/notification#NotificationModel',
                    ExpectedIncome: '../models/statistics#ExpectedIncomeModel',
                    CommentNotification: '../models/notification#NotificationModel',
                    MessageNotification: '../models/notification#NotificationModel',
                    Poll: '../models/post#PollModel',
                    Post: '../models/post#PostModel',
                    CompleteContentPost: '../models/post#PostModel',
                    LimitedContentPost: '../models/post#PostModel',
                    Comment: '../models/post#CommentModel',
                    CommentParent: '../models/post#ParentModel',
                    PostAsset: '../models/post#PostAssetModel',
                    User: '../models/user#UserModel',
                    UserDetails: '../models/user#UserDetailsModel',
                    LivestreamDetails: '../models/user#LivestreamDetailsModel',
                    MessageThread: '../models/message-thread#MessageThreadModel',
                    MessageThreadDetails: '../models/message-thread#MessageThreadDetailsModel',
                    Message: '../models/message-thread#MessageModel',
                    MessageAsset: '../models/message-thread#MessageAssetModel',
                    UserSubscription: '../models/subscription#SubscriptionModel',
                    UserSubscriptionDetails: '../models/subscription#FullSubscriptionModel',
                    SubscribeRequest: '../models/subscription#SubscribeRequestModel',
                },
                namingConvention: {
                    typeNames: 'change-case-all#pascalCase',
                    enumValues: 'change-case-all#upperCase',
                },
                scalars: {
                    DateTime: DateTimeResolver.extensions.codegenScalarType,
                },
            },
        },
    },
}

export default config
