// Here we distinguish services which needs authorization via cookies and
// therefore needs to run on the same domains. We setup 6 domains for such:
//
// with cookie authorization:
//   svc-devel.herohero.co, svc-staging.herohero.co, svc-prod.herohero.co
//
// without cookie authorization:
//   svc-devel.herohero.co, svc-staging.herohero.co, svc-prod.herohero.co
//
// All our Cloud Run services needs auth, therefore run via svc-*.herohero.co domains.
// Note that tag-manager runs on gtm.herohero.co and therefore is not touched by this.
//
// Most of our Cloud Functions don't need the auth, therefore will run on svc-*-na.herohero.co.

variable "cloud_run_services" {
  default = ["api", "auth", "media", "graphql", "tag-manager", "stripe", "connect"]
}

variable "cloud_functions" {
  default = [
    "airtable-feedback"
  ]
}

variable "cloud_functions_non_auth" {
  default = [
    "gift-voucher", "invoice-generator", "invoice-report-generator",
    "notification-disabler", "partner-id-verifier",
    "product-feed-generator", "rss-feed", "sitemap-generator",
    "accounts-flexibee-reporter", "creator-privacy-policy-generator",
    "email-validator"
  ]
}

resource "google_compute_ssl_certificate" "default" {
  name_prefix = "wildcard-herohero-co-"
  private_key = file("ssl/selfsigned.key")
  certificate = file("ssl/selfsigned.crt")

  lifecycle {
    create_before_destroy = true
  }
}

resource "google_compute_target_https_proxy" "default_loadbalancer_target_proxy" {
  project          = var.project
  name             = "default-loadbalancer-target-proxy"
  quic_override    = "NONE"
  ssl_certificates = [google_compute_ssl_certificate.default.id]
  url_map          = google_compute_url_map.default_loadbalancer.id
}

resource "google_compute_url_map" "default_loadbalancer" {
  project         = var.project
  default_service = google_compute_backend_service.cloud_run_devel[0].id
  name            = "default-loadbalancer"

  host_rule {
    hosts = [
      "svc-devel.herohero.co",
    ]
    path_matcher = "path-matcher-devel"
  }

  host_rule {
    hosts = [
      "svc-staging.herohero.co",
    ]
    path_matcher = "path-matcher-staging"
  }

  host_rule {
    hosts = [
      "svc-prod.herohero.co",
    ]
    path_matcher = "path-matcher-prod"
  }

  host_rule {
    hosts = [
      "gtm.herohero.co",
    ]
    path_matcher = "path-matcher-prod-tag-manager"
  }

  host_rule {
    hosts = [
      "gtm-devel.herohero.co",
    ]
    path_matcher = "path-matcher-devel-tag-manager"
  }

  host_rule {
    hosts = [
      "static.herohero.co",
    ]
    path_matcher = "path-matcher-static"
  }

  host_rule {
    hosts = [
      "svc-devel-na.herohero.co",
    ]
    path_matcher = "path-matcher-devel-non-auth"
  }

  host_rule {
    hosts = [
      "svc-staging-na.herohero.co",
    ]
    path_matcher = "path-matcher-staging-non-auth"
  }

  host_rule {
    hosts = [
      "svc-prod-na.herohero.co",
    ]
    path_matcher = "path-matcher-prod-non-auth"
  }

  path_matcher {
    name            = "path-matcher-devel"
    default_service = google_compute_backend_service.cloud_run_devel[0].id

    dynamic "path_rule" {
      for_each = var.cloud_run_services
      content {
        paths   = ["/${var.cloud_run_services[path_rule.key]}/*"]
        service = google_compute_backend_service.cloud_run_devel[path_rule.key].id

        route_action {
          url_rewrite {
            path_prefix_rewrite = "/"
          }
        }
      }
    }

    dynamic "path_rule" {
      for_each = var.cloud_functions
      content {
        paths   = ["/${var.cloud_functions[path_rule.key]}/*"]
        service = google_compute_backend_service.cloud_function_devel[path_rule.key].id

        route_action {
          url_rewrite {
            path_prefix_rewrite = "/"
          }
        }
      }
    }
  }

  path_matcher {
    name            = "path-matcher-staging"
    default_service = google_compute_backend_service.cloud_run_staging[0].id

    dynamic "path_rule" {
      for_each = var.cloud_run_services
      content {
        paths   = ["/${var.cloud_run_services[path_rule.key]}/*"]
        service = google_compute_backend_service.cloud_run_staging[path_rule.key].id

        route_action {
          url_rewrite {
            path_prefix_rewrite = "/"
          }
        }
      }
    }

    dynamic "path_rule" {
      for_each = var.cloud_functions
      content {
        paths   = ["/${var.cloud_functions[path_rule.key]}/*"]
        service = google_compute_backend_service.cloud_function_staging[path_rule.key].id

        route_action {
          url_rewrite {
            path_prefix_rewrite = "/"
          }
        }
      }
    }
  }

  path_matcher {
    name            = "path-matcher-prod"
    default_service = google_compute_backend_service.cloud_run_prod[0].id

    dynamic "path_rule" {
      for_each = var.cloud_run_services
      content {
        paths   = ["/${var.cloud_run_services[path_rule.key]}/*"]
        service = google_compute_backend_service.cloud_run_prod[path_rule.key].id

        route_action {
          url_rewrite {
            path_prefix_rewrite = "/"
          }
        }
      }
    }

    dynamic "path_rule" {
      for_each = var.cloud_functions
      content {
        paths   = ["/${var.cloud_functions[path_rule.key]}/*"]
        service = google_compute_backend_service.cloud_function_prod[path_rule.key].id

        route_action {
          url_rewrite {
            path_prefix_rewrite = "/"
          }
        }
      }
    }
  }

  path_matcher {
    name            = "path-matcher-devel-non-auth"
    default_service = google_compute_backend_service.cloud_function_devel_non_auth[0].id

    // note there is currently no non-auth devel cloud run service, only functions
    dynamic "path_rule" {
      for_each = var.cloud_functions_non_auth
      content {
        paths   = ["/${var.cloud_functions_non_auth[path_rule.key]}/*"]
        service = google_compute_backend_service.cloud_function_devel_non_auth[path_rule.key].id

        route_action {
          url_rewrite {
            path_prefix_rewrite = "/"
          }
        }
      }
    }
  }

  path_matcher {
    name            = "path-matcher-staging-non-auth"
    default_service = google_compute_backend_service.cloud_function_staging_non_auth[0].id

    dynamic "path_rule" {
      for_each = var.cloud_functions_non_auth
      content {
        paths   = ["/${var.cloud_functions_non_auth[path_rule.key]}/*"]
        service = google_compute_backend_service.cloud_function_staging_non_auth[path_rule.key].id

        route_action {
          url_rewrite {
            path_prefix_rewrite = "/"
          }
        }
      }
    }
  }

  path_matcher {
    name            = "path-matcher-prod-non-auth"
    default_service = google_compute_backend_service.cloud_function_prod_non_auth[0].id

    dynamic "path_rule" {
      for_each = var.cloud_functions_non_auth
      content {
        paths   = ["/${var.cloud_functions_non_auth[path_rule.key]}/*"]
        service = google_compute_backend_service.cloud_function_prod_non_auth[path_rule.key].id

        route_action {
          url_rewrite {
            path_prefix_rewrite = "/"
          }
        }
      }
    }
  }

  path_matcher {
    default_service = google_compute_backend_service.cloud_run_prod[index(var.cloud_run_services, "tag-manager")].id
    name            = "path-matcher-prod-tag-manager"
  }

  path_matcher {
    default_service = google_compute_backend_service.cloud_run_devel[index(var.cloud_run_services, "tag-manager")].id
    name            = "path-matcher-devel-tag-manager"
  }

  path_matcher {
    name            = "path-matcher-static"
    default_service = google_compute_backend_bucket.backend_bucket_static.id
  }

  test {
    host    = "svc-devel.herohero.co"
    path    = "/api"
    service = google_compute_backend_service.cloud_run_devel[0].id
  }

  test {
    host    = "svc-staging.herohero.co"
    path    = "/api"
    service = google_compute_backend_service.cloud_run_staging[0].id
  }

  test {
    host    = "svc-prod.herohero.co"
    path    = "/api"
    service = google_compute_backend_service.cloud_run_prod[0].id
  }

  test {
    host    = "static.herohero.co"
    path    = "/"
    service = google_compute_backend_bucket.backend_bucket_static.id
  }

  test {
    host    = "svc-devel-na.herohero.co"
    path    = "/gift-voucher/"
    service = google_compute_backend_service.cloud_function_devel_non_auth[0].id
  }

  test {
    host    = "svc-staging-na.herohero.co"
    path    = "/gift-voucher/"
    service = google_compute_backend_service.cloud_function_staging_non_auth[0].id
  }

  test {
    host    = "svc-prod-na.herohero.co"
    path    = "/gift-voucher/"
    service = google_compute_backend_service.cloud_function_prod_non_auth[0].id
  }
}

resource "google_compute_region_network_endpoint_group" "cloud_run_endpoints_devel" {
  count                 = length(var.cloud_run_services)
  name                  = "cloud-run-devel-${var.cloud_run_services[count.index]}"
  network_endpoint_type = "SERVERLESS"
  region                = "europe-west1"
  cloud_run {
    service = "devel-${var.cloud_run_services[count.index]}"
  }
}

resource "google_compute_region_network_endpoint_group" "cloud_run_endpoints_staging" {
  count                 = length(var.cloud_run_services)
  name                  = "cloud-run-staging-${var.cloud_run_services[count.index]}"
  network_endpoint_type = "SERVERLESS"
  region                = "europe-west1"
  cloud_run {
    service = "staging-${var.cloud_run_services[count.index]}"
  }
}

resource "google_compute_region_network_endpoint_group" "cloud_run_endpoints_prod" {
  count                 = length(var.cloud_run_services)
  name                  = "cloud-run-prod-${var.cloud_run_services[count.index]}"
  network_endpoint_type = "SERVERLESS"
  region                = "europe-west1"
  cloud_run {
    service = "prod-${var.cloud_run_services[count.index]}"
  }
}

resource "google_compute_region_network_endpoint_group" "cloud_functions_endpoints_devel" {
  count                 = length(var.cloud_functions)
  name                  = "cloud-function-devel-${var.cloud_functions[count.index]}"
  network_endpoint_type = "SERVERLESS"
  region                = "europe-west1"
  cloud_function {
    function = "devel-${var.cloud_functions[count.index]}"
  }
}

resource "google_compute_region_network_endpoint_group" "cloud_functions_endpoints_staging" {
  count                 = length(var.cloud_functions)
  name                  = "cloud-function-staging-${var.cloud_functions[count.index]}"
  network_endpoint_type = "SERVERLESS"
  region                = "europe-west1"
  cloud_function {
    function = "staging-${var.cloud_functions[count.index]}"
  }
}

resource "google_compute_region_network_endpoint_group" "cloud_functions_endpoints_prod" {
  count                 = length(var.cloud_functions)
  name                  = "cloud-function-prod-${var.cloud_functions[count.index]}"
  network_endpoint_type = "SERVERLESS"
  region                = "europe-west1"
  cloud_function {
    function = "prod-${var.cloud_functions[count.index]}"
  }
}

resource "google_compute_region_network_endpoint_group" "cloud_functions_endpoints_devel_non_auth" {
  count                 = length(var.cloud_functions_non_auth)
  name                  = "cloud-function-devel-na-${var.cloud_functions_non_auth[count.index]}"
  network_endpoint_type = "SERVERLESS"
  region                = "europe-west1"
  cloud_function {
    function = "devel-${var.cloud_functions_non_auth[count.index]}"
  }
}

resource "google_compute_region_network_endpoint_group" "cloud_functions_endpoints_staging_non_auth" {
  count                 = length(var.cloud_functions_non_auth)
  name                  = "cloud-function-staging-na-${var.cloud_functions_non_auth[count.index]}"
  network_endpoint_type = "SERVERLESS"
  region                = "europe-west1"
  cloud_function {
    function = "staging-${var.cloud_functions_non_auth[count.index]}"
  }
}

resource "google_compute_region_network_endpoint_group" "cloud_functions_endpoints_prod_non_auth" {
  count                 = length(var.cloud_functions_non_auth)
  name                  = "cloud-function-prod-na-${var.cloud_functions_non_auth[count.index]}"
  network_endpoint_type = "SERVERLESS"
  region                = "europe-west1"
  cloud_function {
    function = "prod-${var.cloud_functions_non_auth[count.index]}"
  }
}

resource "google_compute_backend_service" "cloud_run_devel" {
  count                           = length(var.cloud_run_services)
  connection_draining_timeout_sec = 0
  load_balancing_scheme           = "EXTERNAL_MANAGED"
  project                         = var.project
  name                            = "cloud-run-devel-${var.cloud_run_services[count.index]}"
  port_name                       = "http"
  protocol                        = "HTTPS"
  session_affinity                = "NONE"
  timeout_sec                     = 30

  log_config {
    enable      = false
    sample_rate = 1
  }

  backend {
    group                        = google_compute_region_network_endpoint_group.cloud_run_endpoints_devel[count.index].id
    balancing_mode               = "UTILIZATION"
    capacity_scaler              = 1
    max_connections              = 0
    max_connections_per_endpoint = 0
    max_connections_per_instance = 0
    max_rate                     = 0
    max_rate_per_endpoint        = 0
    max_rate_per_instance        = 0
    max_utilization              = 0
  }
}

resource "google_compute_backend_service" "cloud_run_staging" {
  count                           = length(var.cloud_run_services)
  connection_draining_timeout_sec = 0
  load_balancing_scheme           = "EXTERNAL_MANAGED"
  project                         = var.project
  name                            = "cloud-run-staging-${var.cloud_run_services[count.index]}"
  port_name                       = "http"
  protocol                        = "HTTPS"
  session_affinity                = "NONE"
  timeout_sec                     = 30

  log_config {
    enable      = false
    sample_rate = 1
  }

  backend {
    group                        = google_compute_region_network_endpoint_group.cloud_run_endpoints_staging[count.index].id
    balancing_mode               = "UTILIZATION"
    capacity_scaler              = 1
    max_connections              = 0
    max_connections_per_endpoint = 0
    max_connections_per_instance = 0
    max_rate                     = 0
    max_rate_per_endpoint        = 0
    max_rate_per_instance        = 0
    max_utilization              = 0
  }
}

resource "google_compute_backend_service" "cloud_run_prod" {
  count = length(var.cloud_run_services)

  connection_draining_timeout_sec = 0
  load_balancing_scheme           = "EXTERNAL_MANAGED"
  project                         = var.project
  name                            = "cloud-run-prod-${var.cloud_run_services[count.index]}"
  port_name                       = "http"
  protocol                        = "HTTPS"
  session_affinity                = "NONE"
  timeout_sec                     = 30

  log_config {
    enable      = false
    sample_rate = 1
  }

  backend {
    group                        = google_compute_region_network_endpoint_group.cloud_run_endpoints_prod[count.index].id
    balancing_mode               = "UTILIZATION"
    capacity_scaler              = 1
    max_connections              = 0
    max_connections_per_endpoint = 0
    max_connections_per_instance = 0
    max_rate                     = 0
    max_rate_per_endpoint        = 0
    max_rate_per_instance        = 0
    max_utilization              = 0
  }
}

resource "google_compute_backend_service" "cloud_function_devel" {
  count                           = length(var.cloud_functions)
  connection_draining_timeout_sec = 0
  load_balancing_scheme           = "EXTERNAL_MANAGED"
  project                         = var.project
  name                            = "cloud-function-devel-${var.cloud_functions[count.index]}"
  port_name                       = "http"
  protocol                        = "HTTPS"
  session_affinity                = "NONE"
  timeout_sec                     = 30

  log_config {
    enable      = false
    sample_rate = 1
  }

  backend {
    group                        = google_compute_region_network_endpoint_group.cloud_functions_endpoints_devel[count.index].id
    balancing_mode               = "UTILIZATION"
    capacity_scaler              = 1
    max_connections              = 0
    max_connections_per_endpoint = 0
    max_connections_per_instance = 0
    max_rate                     = 0
    max_rate_per_endpoint        = 0
    max_rate_per_instance        = 0
    max_utilization              = 0
  }
}

resource "google_compute_backend_service" "cloud_function_staging" {
  count                           = length(var.cloud_functions)
  connection_draining_timeout_sec = 0
  load_balancing_scheme           = "EXTERNAL_MANAGED"
  project                         = var.project
  name                            = "cloud-function-staging-${var.cloud_functions[count.index]}"
  port_name                       = "http"
  protocol                        = "HTTPS"
  session_affinity                = "NONE"
  timeout_sec                     = 30

  log_config {
    enable      = false
    sample_rate = 1
  }

  backend {
    group                        = google_compute_region_network_endpoint_group.cloud_functions_endpoints_staging[count.index].id
    balancing_mode               = "UTILIZATION"
    capacity_scaler              = 1
    max_connections              = 0
    max_connections_per_endpoint = 0
    max_connections_per_instance = 0
    max_rate                     = 0
    max_rate_per_endpoint        = 0
    max_rate_per_instance        = 0
    max_utilization              = 0
  }
}

resource "google_compute_backend_service" "cloud_function_prod" {
  count                           = length(var.cloud_functions)
  connection_draining_timeout_sec = 0
  load_balancing_scheme           = "EXTERNAL_MANAGED"
  project                         = var.project
  name                            = "cloud-function-prod-${var.cloud_functions[count.index]}"
  port_name                       = "http"
  protocol                        = "HTTPS"
  session_affinity                = "NONE"
  timeout_sec                     = 30

  log_config {
    enable      = false
    sample_rate = 1
  }

  backend {
    group                        = google_compute_region_network_endpoint_group.cloud_functions_endpoints_prod[count.index].id
    balancing_mode               = "UTILIZATION"
    capacity_scaler              = 1
    max_connections              = 0
    max_connections_per_endpoint = 0
    max_connections_per_instance = 0
    max_rate                     = 0
    max_rate_per_endpoint        = 0
    max_rate_per_instance        = 0
    max_utilization              = 0
  }
}

resource "google_compute_backend_bucket" "backend_bucket_static" {
  name        = "backend-bucket-static"
  bucket_name = google_storage_bucket.heroheroco_assets_static.id
}

resource "google_compute_backend_service" "cloud_function_devel_non_auth" {
  count                           = length(var.cloud_functions_non_auth)
  connection_draining_timeout_sec = 0
  load_balancing_scheme           = "EXTERNAL_MANAGED"
  project                         = var.project
  name                            = "cloud-function-devel-na-${var.cloud_functions_non_auth[count.index]}"
  port_name                       = "http"
  protocol                        = "HTTPS"
  session_affinity                = "NONE"
  timeout_sec                     = 30

  log_config {
    enable      = false
    sample_rate = 1
  }

  backend {
    group                        = google_compute_region_network_endpoint_group.cloud_functions_endpoints_devel_non_auth[count.index].id
    balancing_mode               = "UTILIZATION"
    capacity_scaler              = 1
    max_connections              = 0
    max_connections_per_endpoint = 0
    max_connections_per_instance = 0
    max_rate                     = 0
    max_rate_per_endpoint        = 0
    max_rate_per_instance        = 0
    max_utilization              = 0
  }
}

resource "google_compute_backend_service" "cloud_function_staging_non_auth" {
  count                           = length(var.cloud_functions_non_auth)
  connection_draining_timeout_sec = 0
  load_balancing_scheme           = "EXTERNAL_MANAGED"
  project                         = var.project
  name                            = "cloud-function-staging-na-${var.cloud_functions_non_auth[count.index]}"
  port_name                       = "http"
  protocol                        = "HTTPS"
  session_affinity                = "NONE"
  timeout_sec                     = 30

  log_config {
    enable      = false
    sample_rate = 1
  }

  backend {
    group                        = google_compute_region_network_endpoint_group.cloud_functions_endpoints_staging_non_auth[count.index].id
    balancing_mode               = "UTILIZATION"
    capacity_scaler              = 1
    max_connections              = 0
    max_connections_per_endpoint = 0
    max_connections_per_instance = 0
    max_rate                     = 0
    max_rate_per_endpoint        = 0
    max_rate_per_instance        = 0
    max_utilization              = 0
  }
}

resource "google_compute_backend_service" "cloud_function_prod_non_auth" {
  count                           = length(var.cloud_functions_non_auth)
  connection_draining_timeout_sec = 0
  load_balancing_scheme           = "EXTERNAL_MANAGED"
  project                         = var.project
  name                            = "cloud-function-prod-na-${var.cloud_functions_non_auth[count.index]}"
  port_name                       = "http"
  protocol                        = "HTTPS"
  session_affinity                = "NONE"
  timeout_sec                     = 30

  log_config {
    enable      = false
    sample_rate = 1
  }

  backend {
    group                        = google_compute_region_network_endpoint_group.cloud_functions_endpoints_prod_non_auth[count.index].id
    balancing_mode               = "UTILIZATION"
    capacity_scaler              = 1
    max_connections              = 0
    max_connections_per_endpoint = 0
    max_connections_per_instance = 0
    max_rate                     = 0
    max_rate_per_endpoint        = 0
    max_rate_per_instance        = 0
    max_utilization              = 0
  }
}

resource "google_compute_global_forwarding_rule" "cloud_forwarding" {
  project               = var.project
  ip_protocol           = "TCP"
  ip_version            = "IPV4"
  load_balancing_scheme = "EXTERNAL_MANAGED"
  name                  = "cloud-forwarding"
  port_range            = "443-443"
  target                = google_compute_target_https_proxy.default_loadbalancer_target_proxy.id
}
